'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent, Button, Input, Modal } from '@/components/ui';
import { Grid } from '@/components/layout';
import {
  Users,
  Search,
  Filter,
  MoreVertical,
  Shield,
  ShieldCheck,
  ShieldX,
  UserCheck,
  UserX,
  Eye,
  Edit,
  Trash2,
  Wallet,
  Plus,
  Minus,
  DollarSign,
  ChevronDown
} from 'lucide-react';
import { formatDateTime, formatCurrency } from '@/lib/utils';

interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: 'USER' | 'ADMIN';
  kycStatus: 'PENDING' | 'APPROVED' | 'REJECTED';
  isActive: boolean;
  createdAt: string;
  referralId: string;
  totalInvestment?: number;
  totalEarnings?: number;
  walletBalance?: {
    availableBalance: number;
  };
}

interface UserDetails {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: 'USER' | 'ADMIN';
  kycStatus: 'PENDING' | 'APPROVED' | 'REJECTED';
  isActive: boolean;
  createdAt: string;
  referralId: string;
  referrerId?: string;
  walletBalance: {
    availableBalance: number;
    totalEarnings: number;
    totalWithdrawals: number;
    totalDeposits: number;
  };
  miningUnits: {
    totalUnits: number;
    totalInvestment: number;
    totalTHS: number;
    activeTHS: number;
  };
  binaryPoints: {
    leftPoints: number;
    rightPoints: number;
    totalMatched: number;
    lastMatchDate?: string;
  };
  referralStats: {
    directReferrals: number;
    leftTeam: number;
    rightTeam: number;
    totalTeam: number;
  };
  recentActivity: Array<{
    type: string;
    description: string;
    amount?: number;
    date: string;
  }>;
}

interface WalletAdjustment {
  userId: string;
  userName: string;
  userEmail: string;
  amount: string;
  type: 'CREDIT' | 'DEBIT';
  reason: string;
  description: string;
}

export const UserManagement: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'inactive' | 'pending_kyc'>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);

  // Wallet management state
  const [showWalletModal, setShowWalletModal] = useState(false);
  const [walletAdjustment, setWalletAdjustment] = useState<WalletAdjustment>({
    userId: '',
    userName: '',
    userEmail: '',
    amount: '',
    type: 'CREDIT',
    reason: '',
    description: '',
  });
  const [walletLoading, setWalletLoading] = useState(false);
  const [walletError, setWalletError] = useState('');
  const [walletSuccess, setWalletSuccess] = useState('');

  // User details modal state
  const [showUserDetailsModal, setShowUserDetailsModal] = useState(false);
  const [selectedUserDetails, setSelectedUserDetails] = useState<UserDetails | null>(null);
  const [userDetailsLoading, setUserDetailsLoading] = useState(false);

  useEffect(() => {
    fetchUsers();
  }, [currentPage, searchTerm, filterStatus]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (openDropdown && !(event.target as Element).closest('.relative')) {
        setOpenDropdown(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [openDropdown]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20',
        search: searchTerm,
        status: filterStatus,
      });

      const response = await fetch(`/api/admin/users?${params}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setUsers(data.data.users);
          setTotalPages(data.data.totalPages);
        }
      }
    } catch (error) {
      console.error('Failed to fetch users:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUserAction = async (userId: string, action: 'activate' | 'deactivate' | 'promote' | 'demote') => {
    try {
      const response = await fetch('/api/admin/users/action', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ userId, action }),
      });

      if (response.ok) {
        fetchUsers(); // Refresh the list
      }
    } catch (error) {
      console.error('Failed to perform user action:', error);
    }
  };

  const handleWalletAdjustment = (user: User, type: 'CREDIT' | 'DEBIT') => {
    setWalletAdjustment({
      userId: user.id,
      userName: `${user.firstName} ${user.lastName}`,
      userEmail: user.email,
      amount: '',
      type,
      reason: '',
      description: '',
    });
    setWalletError('');
    setWalletSuccess('');
    setShowWalletModal(true);
  };

  const submitWalletAdjustment = async () => {
    if (!walletAdjustment.amount || !walletAdjustment.reason) {
      setWalletError('Amount and reason are required');
      return;
    }

    try {
      setWalletLoading(true);
      setWalletError('');
      setWalletSuccess('');

      const response = await fetch('/api/admin/wallet/adjust', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          userId: walletAdjustment.userId,
          amount: parseFloat(walletAdjustment.amount),
          type: walletAdjustment.type,
          reason: walletAdjustment.reason,
          description: walletAdjustment.description,
        }),
      });

      const data = await response.json();

      if (data.success) {
        setWalletSuccess(`Wallet ${walletAdjustment.type.toLowerCase()} completed successfully`);
        setTimeout(() => {
          setShowWalletModal(false);
          fetchUsers(); // Refresh user list
        }, 2000);
      } else {
        setWalletError(data.error || 'Failed to adjust wallet balance');
      }
    } catch (error) {
      setWalletError('An error occurred while adjusting wallet balance');
    } finally {
      setWalletLoading(false);
    }
  };

  const fetchUserDetails = async (userId: string) => {
    try {
      setUserDetailsLoading(true);
      const response = await fetch(`/api/admin/users/${userId}/details`, {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setSelectedUserDetails(data.data);
          setShowUserDetailsModal(true);
        }
      }
    } catch (error) {
      console.error('Failed to fetch user details:', error);
    } finally {
      setUserDetailsLoading(false);
    }
  };

  const getKYCStatusIcon = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return <ShieldCheck className="h-4 w-4 text-green-400" />;
      case 'REJECTED':
        return <ShieldX className="h-4 w-4 text-red-400" />;
      default:
        return <Shield className="h-4 w-4 text-yellow-400" />;
    }
  };

  const getStatusBadge = (isActive: boolean) => {
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
        isActive
          ? 'bg-blue-600 text-white'
          : 'bg-red-600 text-white'
      }`}>
        {isActive ? 'Active' : 'Inactive'}
      </span>
    );
  };

  const getRoleBadge = (role: string) => {
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
        role === 'ADMIN'
          ? 'bg-red-600 text-white'
          : 'bg-blue-600 text-white'
      }`}>
        {role}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-slate-700 rounded w-1/4 mb-4"></div>
          <div className="h-64 bg-slate-700 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">User Management</h1>
          <p className="text-slate-400 mt-1">Manage platform users and their permissions</p>
        </div>
      </div>

      {/* Filters */}
      <Card className="bg-slate-800 border-slate-700">
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  placeholder="Search users by email or name..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400"
                />
              </div>
            </div>
            <div className="sm:w-48">
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value as any)}
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Users</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="pending_kyc">Pending KYC</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Users Table */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white">
            <Users className="h-5 w-5" />
            Users ({users.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-slate-600">
                  <th className="text-left py-3 px-4 font-medium text-white">User</th>
                  <th className="text-left py-3 px-4 font-medium text-white">Role</th>
                  <th className="text-left py-3 px-4 font-medium text-white">KYC Status</th>
                  <th className="text-left py-3 px-4 font-medium text-white">Status</th>
                  <th className="text-left py-3 px-4 font-medium text-white">Wallet Balance</th>
                  <th className="text-left py-3 px-4 font-medium text-white">Joined</th>
                  <th className="text-left py-3 px-4 font-medium text-white">Actions</th>
                </tr>
              </thead>
              <tbody>
                {users.map((user) => (
                  <tr key={user.id} className="border-b border-slate-700 hover:bg-slate-700">
                    <td className="py-4 px-4">
                      <div>
                        <div className="font-medium text-white">
                          {user.firstName} {user.lastName}
                        </div>
                        <div className="text-sm text-slate-400">{user.email}</div>
                        <div className="text-xs text-slate-500">ID: {user.referralId}</div>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      {getRoleBadge(user.role)}
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-2">
                        {getKYCStatusIcon(user.kycStatus)}
                        <span className="text-sm text-slate-300">{user.kycStatus}</span>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      {getStatusBadge(user.isActive)}
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-2">
                        <Wallet className="h-4 w-4 text-green-400" />
                        <span className="text-sm font-medium text-green-400">
                          {formatCurrency(user.walletBalance?.availableBalance || 0)}
                        </span>
                      </div>
                    </td>
                    <td className="py-4 px-4 text-sm text-slate-400">
                      {formatDateTime(user.createdAt)}
                    </td>
                    <td className="py-4 px-4">
                      <div className="relative">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setOpenDropdown(openDropdown === user.id ? null : user.id)}
                          className="border-slate-600 text-slate-300 hover:bg-slate-700 flex items-center gap-2"
                        >
                          <MoreVertical className="h-4 w-4" />
                          <ChevronDown className="h-3 w-3" />
                        </Button>

                        {openDropdown === user.id && (
                          <div className="absolute right-0 top-full mt-1 w-48 bg-slate-800 border border-slate-600 rounded-md shadow-lg z-10">
                            <div className="py-1">
                              <button
                                onClick={() => {
                                  handleUserAction(user.id, user.isActive ? 'deactivate' : 'activate');
                                  setOpenDropdown(null);
                                }}
                                className="flex items-center gap-2 w-full px-4 py-2 text-sm text-slate-300 hover:bg-slate-700"
                              >
                                {user.isActive ? <UserX className="h-4 w-4" /> : <UserCheck className="h-4 w-4" />}
                                {user.isActive ? 'Deactivate User' : 'Activate User'}
                              </button>

                              {user.role === 'USER' && (
                                <button
                                  onClick={() => {
                                    handleUserAction(user.id, 'promote');
                                    setOpenDropdown(null);
                                  }}
                                  className="flex items-center gap-2 w-full px-4 py-2 text-sm text-slate-300 hover:bg-slate-700"
                                >
                                  <Shield className="h-4 w-4" />
                                  Promote to Admin
                                </button>
                              )}

                              <div className="border-t border-slate-600 my-1"></div>

                              <button
                                onClick={() => {
                                  handleWalletAdjustment(user, 'CREDIT');
                                  setOpenDropdown(null);
                                }}
                                className="flex items-center gap-2 w-full px-4 py-2 text-sm text-green-300 hover:bg-slate-700"
                              >
                                <Plus className="h-4 w-4" />
                                Credit Wallet
                              </button>

                              <button
                                onClick={() => {
                                  handleWalletAdjustment(user, 'DEBIT');
                                  setOpenDropdown(null);
                                }}
                                className="flex items-center gap-2 w-full px-4 py-2 text-sm text-red-300 hover:bg-slate-700"
                              >
                                <Minus className="h-4 w-4" />
                                Debit Wallet
                              </button>

                              <div className="border-t border-slate-600 my-1"></div>

                              <button
                                onClick={() => {
                                  fetchUserDetails(user.id);
                                  setOpenDropdown(null);
                                }}
                                className="flex items-center gap-2 w-full px-4 py-2 text-sm text-slate-300 hover:bg-slate-700"
                                disabled={userDetailsLoading}
                              >
                                <Eye className="h-4 w-4" />
                                {userDetailsLoading ? 'Loading...' : 'View Details'}
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <div className="text-sm text-slate-400">
                Page {currentPage} of {totalPages}
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                  className="border-slate-600 text-slate-300 hover:bg-slate-700"
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                  className="border-slate-600 text-slate-300 hover:bg-slate-700"
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Wallet Adjustment Modal */}
      {showWalletModal && (
        <Modal
          isOpen={showWalletModal}
          onClose={() => setShowWalletModal(false)}
          title={`${walletAdjustment.type === 'CREDIT' ? 'Credit' : 'Debit'} User Wallet`}
          darkMode={true}
        >
          <div className="space-y-4">
            <div className="bg-slate-700 p-4 rounded-lg">
              <h3 className="font-medium text-white mb-2">User Information</h3>
              <p className="text-slate-300 text-sm">Name: {walletAdjustment.userName}</p>
              <p className="text-slate-300 text-sm">Email: {walletAdjustment.userEmail}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Amount (USDT)
              </label>
              <Input
                type="number"
                step="0.01"
                min="0"
                value={walletAdjustment.amount}
                onChange={(e) => setWalletAdjustment(prev => ({ ...prev, amount: e.target.value }))}
                placeholder="Enter amount"
                className="bg-slate-700 border-slate-600 text-white"
                disabled={walletLoading}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Reason *
              </label>
              <select
                value={walletAdjustment.reason}
                onChange={(e) => setWalletAdjustment(prev => ({ ...prev, reason: e.target.value }))}
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white rounded-lg"
                disabled={walletLoading}
              >
                <option value="">Select reason</option>
                <option value="Manual Adjustment">Manual Adjustment</option>
                <option value="Bonus Payment">Bonus Payment</option>
                <option value="Refund">Refund</option>
                <option value="Correction">Balance Correction</option>
                <option value="Penalty">Penalty</option>
                <option value="Promotion">Promotional Credit</option>
                <option value="Other">Other</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Description (Optional)
              </label>
              <textarea
                value={walletAdjustment.description}
                onChange={(e) => setWalletAdjustment(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Additional details about this adjustment..."
                rows={3}
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white rounded-lg resize-none"
                disabled={walletLoading}
              />
            </div>

            {walletError && (
              <div className="bg-red-900/20 border border-red-500 rounded-lg p-3">
                <p className="text-red-400 text-sm">{walletError}</p>
              </div>
            )}

            {walletSuccess && (
              <div className="bg-green-900/20 border border-green-500 rounded-lg p-3">
                <p className="text-green-400 text-sm">{walletSuccess}</p>
              </div>
            )}

            <div className="flex justify-end space-x-3 pt-4">
              <Button
                variant="outline"
                onClick={() => setShowWalletModal(false)}
                disabled={walletLoading}
                className="border-slate-600 text-slate-300 hover:bg-slate-700"
              >
                Cancel
              </Button>
              <Button
                onClick={submitWalletAdjustment}
                disabled={walletLoading || !walletAdjustment.amount || !walletAdjustment.reason}
                className={`${
                  walletAdjustment.type === 'CREDIT'
                    ? 'bg-green-600 hover:bg-green-700'
                    : 'bg-red-600 hover:bg-red-700'
                } text-white`}
              >
                {walletLoading ? 'Processing...' : `${walletAdjustment.type === 'CREDIT' ? 'Credit' : 'Debit'} Wallet`}
              </Button>
            </div>
          </div>
        </Modal>
      )}

      {/* User Details Modal */}
      {showUserDetailsModal && selectedUserDetails && (
        <Modal
          isOpen={showUserDetailsModal}
          onClose={() => setShowUserDetailsModal(false)}
          title="User Details"
          darkMode={true}
          size="xl"
        >
          <div className="space-y-6 max-h-[80vh] overflow-y-auto">
            {/* Basic Information */}
            <div className="bg-slate-700 p-4 rounded-lg">
              <h3 className="font-medium text-white mb-3 flex items-center gap-2">
                <Users className="h-4 w-4" />
                Basic Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-xs text-slate-400">Full Name</label>
                  <p className="text-white">{selectedUserDetails.firstName} {selectedUserDetails.lastName}</p>
                </div>
                <div>
                  <label className="text-xs text-slate-400">Email</label>
                  <p className="text-white">{selectedUserDetails.email}</p>
                </div>
                <div>
                  <label className="text-xs text-slate-400">Referral ID</label>
                  <p className="text-white font-mono">{selectedUserDetails.referralId}</p>
                </div>
                <div>
                  <label className="text-xs text-slate-400">Role</label>
                  <p className="text-white">{getRoleBadge(selectedUserDetails.role)}</p>
                </div>
                <div>
                  <label className="text-xs text-slate-400">Status</label>
                  <p className="text-white">{getStatusBadge(selectedUserDetails.isActive)}</p>
                </div>
                <div>
                  <label className="text-xs text-slate-400">KYC Status</label>
                  <div className="flex items-center gap-2">
                    {getKYCStatusIcon(selectedUserDetails.kycStatus)}
                    <span className="text-white">{selectedUserDetails.kycStatus}</span>
                  </div>
                </div>
                <div>
                  <label className="text-xs text-slate-400">Joined Date</label>
                  <p className="text-white">{formatDateTime(selectedUserDetails.createdAt)}</p>
                </div>
                {selectedUserDetails.referrerId && (
                  <div>
                    <label className="text-xs text-slate-400">Referred By</label>
                    <p className="text-white font-mono">{selectedUserDetails.referrerId}</p>
                  </div>
                )}
              </div>
            </div>

            {/* Wallet Information */}
            <div className="bg-slate-700 p-4 rounded-lg">
              <h3 className="font-medium text-white mb-3 flex items-center gap-2">
                <Wallet className="h-4 w-4" />
                Wallet Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-xs text-slate-400">Available Balance</label>
                  <p className="text-white text-lg font-semibold">{formatCurrency(selectedUserDetails.walletBalance.availableBalance)}</p>
                </div>
                <div>
                  <label className="text-xs text-slate-400">Total Earnings</label>
                  <p className="text-green-400 text-lg font-semibold">{formatCurrency(selectedUserDetails.walletBalance.totalEarnings)}</p>
                </div>
                <div>
                  <label className="text-xs text-slate-400">Total Deposits</label>
                  <p className="text-blue-400">{formatCurrency(selectedUserDetails.walletBalance.totalDeposits)}</p>
                </div>
                <div>
                  <label className="text-xs text-slate-400">Total Withdrawals</label>
                  <p className="text-red-400">{formatCurrency(selectedUserDetails.walletBalance.totalWithdrawals)}</p>
                </div>
              </div>
            </div>

            {/* Mining Units */}
            <div className="bg-slate-700 p-4 rounded-lg">
              <h3 className="font-medium text-white mb-3 flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                Mining Units
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-xs text-slate-400">Total Units</label>
                  <p className="text-white text-lg font-semibold">{selectedUserDetails.miningUnits.totalUnits}</p>
                </div>
                <div>
                  <label className="text-xs text-slate-400">Total Investment</label>
                  <p className="text-white text-lg font-semibold">{formatCurrency(selectedUserDetails.miningUnits.totalInvestment)}</p>
                </div>
                <div>
                  <label className="text-xs text-slate-400">Total TH/s</label>
                  <p className="text-blue-400">{selectedUserDetails.miningUnits.totalTHS.toFixed(2)} TH/s</p>
                </div>
                <div>
                  <label className="text-xs text-slate-400">Active TH/s</label>
                  <p className="text-green-400">{selectedUserDetails.miningUnits.activeTHS.toFixed(2)} TH/s</p>
                </div>
              </div>
            </div>

            {/* Binary Points */}
            <div className="bg-slate-700 p-4 rounded-lg">
              <h3 className="font-medium text-white mb-3 flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Binary Points
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-xs text-slate-400">Left Points</label>
                  <p className="text-white text-lg font-semibold">{selectedUserDetails.binaryPoints.leftPoints}</p>
                </div>
                <div>
                  <label className="text-xs text-slate-400">Right Points</label>
                  <p className="text-white text-lg font-semibold">{selectedUserDetails.binaryPoints.rightPoints}</p>
                </div>
                <div>
                  <label className="text-xs text-slate-400">Total Matched</label>
                  <p className="text-green-400">{selectedUserDetails.binaryPoints.totalMatched}</p>
                </div>
                <div>
                  <label className="text-xs text-slate-400">Last Match Date</label>
                  <p className="text-white">
                    {selectedUserDetails.binaryPoints.lastMatchDate
                      ? formatDateTime(selectedUserDetails.binaryPoints.lastMatchDate)
                      : 'Never'
                    }
                  </p>
                </div>
              </div>
            </div>

            {/* Referral Statistics */}
            <div className="bg-slate-700 p-4 rounded-lg">
              <h3 className="font-medium text-white mb-3 flex items-center gap-2">
                <Users className="h-4 w-4" />
                Referral Statistics
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-xs text-slate-400">Direct Referrals</label>
                  <p className="text-white text-lg font-semibold">{selectedUserDetails.referralStats.directReferrals}</p>
                </div>
                <div>
                  <label className="text-xs text-slate-400">Total Team</label>
                  <p className="text-white text-lg font-semibold">{selectedUserDetails.referralStats.totalTeam}</p>
                </div>
                <div>
                  <label className="text-xs text-slate-400">Left Team</label>
                  <p className="text-blue-400">{selectedUserDetails.referralStats.leftTeam}</p>
                </div>
                <div>
                  <label className="text-xs text-slate-400">Right Team</label>
                  <p className="text-green-400">{selectedUserDetails.referralStats.rightTeam}</p>
                </div>
              </div>
            </div>

            {/* Recent Activity */}
            {selectedUserDetails.recentActivity.length > 0 && (
              <div className="bg-slate-700 p-4 rounded-lg">
                <h3 className="font-medium text-white mb-3 flex items-center gap-2">
                  <Eye className="h-4 w-4" />
                  Recent Activity
                </h3>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {selectedUserDetails.recentActivity.map((activity, index) => (
                    <div key={index} className="flex justify-between items-center p-2 bg-slate-600 rounded">
                      <div>
                        <p className="text-white text-sm">{activity.description}</p>
                        <p className="text-slate-400 text-xs">{formatDateTime(activity.date)}</p>
                      </div>
                      {activity.amount && (
                        <p className="text-green-400 font-semibold">{formatCurrency(activity.amount)}</p>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </Modal>
      )}
    </div>
  );
};
