'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent, Input, Button } from '@/components/ui';
import { 
  ArrowUpDown, 
  Search, 
  Filter, 
  Download, 
  Eye,
  TrendingUp,
  TrendingDown,
  Users,
  DollarSign
} from 'lucide-react';
import { formatCurrency, formatNumber, formatDateTime } from '@/lib/utils';

interface BinaryPointsData {
  id: string;
  userId: string;
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    createdAt: string;
  };
  leftPoints: number;
  rightPoints: number;
  matchedPoints: number;
  totalMatched: number;
  lastMatchDate: string | null;
  flushDate: string | null;
  createdAt: string;
  updatedAt: string;
}

interface BinaryMatchHistory {
  id: string;
  userId: string;
  user: {
    email: string;
    firstName: string;
    lastName: string;
  };
  matchedPoints: number;
  payout: number;
  leftPointsBefore: number;
  rightPointsBefore: number;
  leftPointsAfter: number;
  rightPointsAfter: number;
  matchDate: string;
  type: 'WEEKLY' | 'MANUAL';
}

export const BinaryPointsManagement: React.FC = () => {
  const [binaryPointsData, setBinaryPointsData] = useState<BinaryPointsData[]>([]);
  const [matchHistory, setMatchHistory] = useState<BinaryMatchHistory[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'inactive'>('all');
  const [selectedUser, setSelectedUser] = useState<string | null>(null);
  const [showHistory, setShowHistory] = useState(false);
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalLeftPoints: 0,
    totalRightPoints: 0,
    totalMatchedPoints: 0,
    totalPayouts: 0,
  });

  useEffect(() => {
    fetchBinaryPointsData();
    fetchMatchHistory();
  }, []);

  const fetchBinaryPointsData = async () => {
    try {
      const response = await fetch('/api/admin/binary-points', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setBinaryPointsData(data.data);
          calculateStats(data.data);
        }
      }
    } catch (error) {
      console.error('Failed to fetch binary points data:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchMatchHistory = async () => {
    try {
      const response = await fetch('/api/admin/binary-points/history', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setMatchHistory(data.data);
        }
      }
    } catch (error) {
      console.error('Failed to fetch match history:', error);
    }
  };

  const calculateStats = (data: BinaryPointsData[]) => {
    const stats = data.reduce((acc, item) => ({
      totalUsers: acc.totalUsers + 1,
      totalLeftPoints: acc.totalLeftPoints + item.leftPoints,
      totalRightPoints: acc.totalRightPoints + item.rightPoints,
      totalMatchedPoints: acc.totalMatchedPoints + item.matchedPoints,
      totalPayouts: acc.totalPayouts + (item.totalMatched * 10), // Assuming $10 per point
    }), {
      totalUsers: 0,
      totalLeftPoints: 0,
      totalRightPoints: 0,
      totalMatchedPoints: 0,
      totalPayouts: 0,
    });

    setStats(stats);
  };

  const filteredData = binaryPointsData.filter(item => {
    const matchesSearch = 
      item.user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.user.lastName.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesFilter = 
      filterStatus === 'all' ||
      (filterStatus === 'active' && (item.leftPoints > 0 || item.rightPoints > 0)) ||
      (filterStatus === 'inactive' && item.leftPoints === 0 && item.rightPoints === 0);

    return matchesSearch && matchesFilter;
  });

  const exportData = () => {
    const csvContent = [
      ['User Email', 'Name', 'Left Points', 'Right Points', 'Matched Points', 'Total Matched', 'Last Match Date'].join(','),
      ...filteredData.map(item => [
        item.user.email,
        `${item.user.firstName} ${item.user.lastName}`,
        item.leftPoints,
        item.rightPoints,
        item.matchedPoints,
        item.totalMatched,
        item.lastMatchDate ? formatDateTime(item.lastMatchDate) : 'Never'
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `binary-points-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const viewUserHistory = (userId: string) => {
    setSelectedUser(userId);
    setShowHistory(true);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-400">Total Users</p>
                <p className="text-2xl font-bold text-white">{formatNumber(stats.totalUsers)}</p>
              </div>
              <Users className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-400">Total Left Points</p>
                <p className="text-2xl font-bold text-white">{formatNumber(stats.totalLeftPoints)}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-400">Total Right Points</p>
                <p className="text-2xl font-bold text-white">{formatNumber(stats.totalRightPoints)}</p>
              </div>
              <TrendingDown className="h-8 w-8 text-red-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-400">Total Matched</p>
                <p className="text-2xl font-bold text-white">{formatNumber(stats.totalMatchedPoints)}</p>
              </div>
              <ArrowUpDown className="h-8 w-8 text-purple-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-400">Total Payouts</p>
                <p className="text-2xl font-bold text-white">{formatCurrency(stats.totalPayouts)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-yellow-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white">
            <ArrowUpDown className="h-5 w-5 text-purple-400" />
            Binary Points Management
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Controls */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  type="text"
                  placeholder="Search by email or name..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400"
                />
              </div>
            </div>
            
            <div className="flex gap-2">
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value as any)}
                className="px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white text-sm"
              >
                <option value="all">All Users</option>
                <option value="active">Active Points</option>
                <option value="inactive">No Points</option>
              </select>
              
              <Button
                onClick={exportData}
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>

          {/* Table */}
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b border-slate-600">
                  <th className="text-left py-3 px-4 text-slate-300">User</th>
                  <th className="text-right py-3 px-4 text-slate-300">Left Points</th>
                  <th className="text-right py-3 px-4 text-slate-300">Right Points</th>
                  <th className="text-right py-3 px-4 text-slate-300">Matchable</th>
                  <th className="text-right py-3 px-4 text-slate-300">Total Matched</th>
                  <th className="text-right py-3 px-4 text-slate-300">Last Match</th>
                  <th className="text-center py-3 px-4 text-slate-300">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredData.map((item) => {
                  const matchablePoints = Math.min(item.leftPoints, item.rightPoints);
                  return (
                    <tr key={item.id} className="border-b border-slate-700 hover:bg-slate-700/50">
                      <td className="py-3 px-4">
                        <div>
                          <div className="font-medium text-white">
                            {item.user.firstName} {item.user.lastName}
                          </div>
                          <div className="text-sm text-slate-400">{item.user.email}</div>
                        </div>
                      </td>
                      <td className="text-right py-3 px-4 text-white">
                        {formatNumber(item.leftPoints)}
                      </td>
                      <td className="text-right py-3 px-4 text-white">
                        {formatNumber(item.rightPoints)}
                      </td>
                      <td className="text-right py-3 px-4">
                        <span className={`font-medium ${matchablePoints > 0 ? 'text-green-400' : 'text-slate-400'}`}>
                          {formatNumber(matchablePoints)}
                        </span>
                      </td>
                      <td className="text-right py-3 px-4 text-white">
                        {formatNumber(item.totalMatched)}
                      </td>
                      <td className="text-right py-3 px-4 text-slate-300">
                        {item.lastMatchDate ? formatDate(item.lastMatchDate) : 'Never'}
                      </td>
                      <td className="text-center py-3 px-4">
                        <Button
                          onClick={() => viewUserHistory(item.userId)}
                          className="bg-blue-600 hover:bg-blue-700 text-white text-xs px-2 py-1"
                        >
                          <Eye className="h-3 w-3 mr-1" />
                          View
                        </Button>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>

          {filteredData.length === 0 && (
            <div className="text-center py-8 text-slate-400">
              No binary points data found matching your criteria.
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
