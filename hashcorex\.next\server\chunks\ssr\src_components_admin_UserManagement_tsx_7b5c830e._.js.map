{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/admin/UserManagement.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent, Button, Input, Modal } from '@/components/ui';\nimport { Grid } from '@/components/layout';\nimport {\n  Users,\n  Search,\n  Filter,\n  MoreVertical,\n  Shield,\n  ShieldCheck,\n  ShieldX,\n  UserCheck,\n  UserX,\n  Eye,\n  Edit,\n  Trash2,\n  Wallet,\n  Plus,\n  Minus,\n  DollarSign,\n  ChevronDown\n} from 'lucide-react';\nimport { formatDateTime, formatCurrency } from '@/lib/utils';\n\ninterface User {\n  id: string;\n  email: string;\n  firstName: string;\n  lastName: string;\n  role: 'USER' | 'ADMIN';\n  kycStatus: 'PENDING' | 'APPROVED' | 'REJECTED';\n  isActive: boolean;\n  createdAt: string;\n  referralId: string;\n  totalInvestment?: number;\n  totalEarnings?: number;\n  walletBalance?: {\n    availableBalance: number;\n  };\n}\n\ninterface UserDetails {\n  id: string;\n  email: string;\n  firstName: string;\n  lastName: string;\n  role: 'USER' | 'ADMIN';\n  kycStatus: 'PENDING' | 'APPROVED' | 'REJECTED';\n  isActive: boolean;\n  createdAt: string;\n  referralId: string;\n  referrerId?: string;\n  walletBalance: {\n    availableBalance: number;\n    totalEarnings: number;\n    totalWithdrawals: number;\n    totalDeposits: number;\n  };\n  miningUnits: {\n    totalUnits: number;\n    totalInvestment: number;\n    totalTHS: number;\n    activeTHS: number;\n  };\n  binaryPoints: {\n    leftPoints: number;\n    rightPoints: number;\n    totalMatched: number;\n    lastMatchDate?: string;\n  };\n  referralStats: {\n    directReferrals: number;\n    leftTeam: number;\n    rightTeam: number;\n    totalTeam: number;\n  };\n  recentActivity: Array<{\n    type: string;\n    description: string;\n    amount?: number;\n    date: string;\n  }>;\n}\n\ninterface WalletAdjustment {\n  userId: string;\n  userName: string;\n  userEmail: string;\n  amount: string;\n  type: 'CREDIT' | 'DEBIT';\n  reason: string;\n  description: string;\n}\n\nexport const UserManagement: React.FC = () => {\n  const [users, setUsers] = useState<User[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'inactive' | 'pending_kyc'>('all');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [openDropdown, setOpenDropdown] = useState<string | null>(null);\n\n  // Wallet management state\n  const [showWalletModal, setShowWalletModal] = useState(false);\n  const [walletAdjustment, setWalletAdjustment] = useState<WalletAdjustment>({\n    userId: '',\n    userName: '',\n    userEmail: '',\n    amount: '',\n    type: 'CREDIT',\n    reason: '',\n    description: '',\n  });\n  const [walletLoading, setWalletLoading] = useState(false);\n  const [walletError, setWalletError] = useState('');\n  const [walletSuccess, setWalletSuccess] = useState('');\n\n  // User details modal state\n  const [showUserDetailsModal, setShowUserDetailsModal] = useState(false);\n  const [selectedUserDetails, setSelectedUserDetails] = useState<UserDetails | null>(null);\n  const [userDetailsLoading, setUserDetailsLoading] = useState(false);\n\n  useEffect(() => {\n    fetchUsers();\n  }, [currentPage, searchTerm, filterStatus]);\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (openDropdown && !(event.target as Element).closest('.relative')) {\n        setOpenDropdown(null);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, [openDropdown]);\n\n  const fetchUsers = async () => {\n    try {\n      setLoading(true);\n      const params = new URLSearchParams({\n        page: currentPage.toString(),\n        limit: '20',\n        search: searchTerm,\n        status: filterStatus,\n      });\n\n      const response = await fetch(`/api/admin/users?${params}`, {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setUsers(data.data.users);\n          setTotalPages(data.data.totalPages);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch users:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleUserAction = async (userId: string, action: 'activate' | 'deactivate' | 'promote' | 'demote') => {\n    try {\n      const response = await fetch('/api/admin/users/action', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include',\n        body: JSON.stringify({ userId, action }),\n      });\n\n      if (response.ok) {\n        fetchUsers(); // Refresh the list\n      }\n    } catch (error) {\n      console.error('Failed to perform user action:', error);\n    }\n  };\n\n  const handleWalletAdjustment = (user: User, type: 'CREDIT' | 'DEBIT') => {\n    setWalletAdjustment({\n      userId: user.id,\n      userName: `${user.firstName} ${user.lastName}`,\n      userEmail: user.email,\n      amount: '',\n      type,\n      reason: '',\n      description: '',\n    });\n    setWalletError('');\n    setWalletSuccess('');\n    setShowWalletModal(true);\n  };\n\n  const submitWalletAdjustment = async () => {\n    if (!walletAdjustment.amount || !walletAdjustment.reason) {\n      setWalletError('Amount and reason are required');\n      return;\n    }\n\n    try {\n      setWalletLoading(true);\n      setWalletError('');\n      setWalletSuccess('');\n\n      const response = await fetch('/api/admin/wallet/adjust', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include',\n        body: JSON.stringify({\n          userId: walletAdjustment.userId,\n          amount: parseFloat(walletAdjustment.amount),\n          type: walletAdjustment.type,\n          reason: walletAdjustment.reason,\n          description: walletAdjustment.description,\n        }),\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        setWalletSuccess(`Wallet ${walletAdjustment.type.toLowerCase()} completed successfully`);\n        setTimeout(() => {\n          setShowWalletModal(false);\n          fetchUsers(); // Refresh user list\n        }, 2000);\n      } else {\n        setWalletError(data.error || 'Failed to adjust wallet balance');\n      }\n    } catch (error) {\n      setWalletError('An error occurred while adjusting wallet balance');\n    } finally {\n      setWalletLoading(false);\n    }\n  };\n\n  const fetchUserDetails = async (userId: string) => {\n    try {\n      setUserDetailsLoading(true);\n      const response = await fetch(`/api/admin/users/${userId}/details`, {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setSelectedUserDetails(data.data);\n          setShowUserDetailsModal(true);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch user details:', error);\n    } finally {\n      setUserDetailsLoading(false);\n    }\n  };\n\n  const getKYCStatusIcon = (status: string) => {\n    switch (status) {\n      case 'APPROVED':\n        return <ShieldCheck className=\"h-4 w-4 text-green-400\" />;\n      case 'REJECTED':\n        return <ShieldX className=\"h-4 w-4 text-red-400\" />;\n      default:\n        return <Shield className=\"h-4 w-4 text-yellow-400\" />;\n    }\n  };\n\n  const getStatusBadge = (isActive: boolean) => {\n    return (\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n        isActive\n          ? 'bg-blue-600 text-white'\n          : 'bg-red-600 text-white'\n      }`}>\n        {isActive ? 'Active' : 'Inactive'}\n      </span>\n    );\n  };\n\n  const getRoleBadge = (role: string) => {\n    return (\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n        role === 'ADMIN'\n          ? 'bg-red-600 text-white'\n          : 'bg-blue-600 text-white'\n      }`}>\n        {role}\n      </span>\n    );\n  };\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-8 bg-slate-700 rounded w-1/4 mb-4\"></div>\n          <div className=\"h-64 bg-slate-700 rounded\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-white\">User Management</h1>\n          <p className=\"text-slate-400 mt-1\">Manage platform users and their permissions</p>\n        </div>\n      </div>\n\n      {/* Filters */}\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardContent className=\"p-6\">\n          <div className=\"flex flex-col sm:flex-row gap-4\">\n            <div className=\"flex-1\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4\" />\n                <Input\n                  placeholder=\"Search users by email or name...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400\"\n                />\n              </div>\n            </div>\n            <div className=\"sm:w-48\">\n              <select\n                value={filterStatus}\n                onChange={(e) => setFilterStatus(e.target.value as any)}\n                className=\"w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                <option value=\"all\">All Users</option>\n                <option value=\"active\">Active</option>\n                <option value=\"inactive\">Inactive</option>\n                <option value=\"pending_kyc\">Pending KYC</option>\n              </select>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Users Table */}\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2 text-white\">\n            <Users className=\"h-5 w-5\" />\n            Users ({users.length})\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"overflow-x-auto\">\n            <table className=\"w-full\">\n              <thead>\n                <tr className=\"border-b border-slate-600\">\n                  <th className=\"text-left py-3 px-4 font-medium text-white\">User</th>\n                  <th className=\"text-left py-3 px-4 font-medium text-white\">Role</th>\n                  <th className=\"text-left py-3 px-4 font-medium text-white\">KYC Status</th>\n                  <th className=\"text-left py-3 px-4 font-medium text-white\">Status</th>\n                  <th className=\"text-left py-3 px-4 font-medium text-white\">Wallet Balance</th>\n                  <th className=\"text-left py-3 px-4 font-medium text-white\">Joined</th>\n                  <th className=\"text-left py-3 px-4 font-medium text-white\">Actions</th>\n                </tr>\n              </thead>\n              <tbody>\n                {users.map((user) => (\n                  <tr key={user.id} className=\"border-b border-slate-700 hover:bg-slate-700\">\n                    <td className=\"py-4 px-4\">\n                      <div>\n                        <div className=\"font-medium text-white\">\n                          {user.firstName} {user.lastName}\n                        </div>\n                        <div className=\"text-sm text-slate-400\">{user.email}</div>\n                        <div className=\"text-xs text-slate-500\">ID: {user.referralId}</div>\n                      </div>\n                    </td>\n                    <td className=\"py-4 px-4\">\n                      {getRoleBadge(user.role)}\n                    </td>\n                    <td className=\"py-4 px-4\">\n                      <div className=\"flex items-center gap-2\">\n                        {getKYCStatusIcon(user.kycStatus)}\n                        <span className=\"text-sm text-slate-300\">{user.kycStatus}</span>\n                      </div>\n                    </td>\n                    <td className=\"py-4 px-4\">\n                      {getStatusBadge(user.isActive)}\n                    </td>\n                    <td className=\"py-4 px-4\">\n                      <div className=\"flex items-center gap-2\">\n                        <Wallet className=\"h-4 w-4 text-green-400\" />\n                        <span className=\"text-sm font-medium text-green-400\">\n                          {formatCurrency(user.walletBalance?.availableBalance || 0)}\n                        </span>\n                      </div>\n                    </td>\n                    <td className=\"py-4 px-4 text-sm text-slate-400\">\n                      {formatDateTime(user.createdAt)}\n                    </td>\n                    <td className=\"py-4 px-4\">\n                      <div className=\"relative\">\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={() => setOpenDropdown(openDropdown === user.id ? null : user.id)}\n                          className=\"border-slate-600 text-slate-300 hover:bg-slate-700 flex items-center gap-2\"\n                        >\n                          <MoreVertical className=\"h-4 w-4\" />\n                          <ChevronDown className=\"h-3 w-3\" />\n                        </Button>\n\n                        {openDropdown === user.id && (\n                          <div className=\"absolute right-0 top-full mt-1 w-48 bg-slate-800 border border-slate-600 rounded-md shadow-lg z-10\">\n                            <div className=\"py-1\">\n                              <button\n                                onClick={() => {\n                                  handleUserAction(user.id, user.isActive ? 'deactivate' : 'activate');\n                                  setOpenDropdown(null);\n                                }}\n                                className=\"flex items-center gap-2 w-full px-4 py-2 text-sm text-slate-300 hover:bg-slate-700\"\n                              >\n                                {user.isActive ? <UserX className=\"h-4 w-4\" /> : <UserCheck className=\"h-4 w-4\" />}\n                                {user.isActive ? 'Deactivate User' : 'Activate User'}\n                              </button>\n\n                              {user.role === 'USER' && (\n                                <button\n                                  onClick={() => {\n                                    handleUserAction(user.id, 'promote');\n                                    setOpenDropdown(null);\n                                  }}\n                                  className=\"flex items-center gap-2 w-full px-4 py-2 text-sm text-slate-300 hover:bg-slate-700\"\n                                >\n                                  <Shield className=\"h-4 w-4\" />\n                                  Promote to Admin\n                                </button>\n                              )}\n\n                              <div className=\"border-t border-slate-600 my-1\"></div>\n\n                              <button\n                                onClick={() => {\n                                  handleWalletAdjustment(user, 'CREDIT');\n                                  setOpenDropdown(null);\n                                }}\n                                className=\"flex items-center gap-2 w-full px-4 py-2 text-sm text-green-300 hover:bg-slate-700\"\n                              >\n                                <Plus className=\"h-4 w-4\" />\n                                Credit Wallet\n                              </button>\n\n                              <button\n                                onClick={() => {\n                                  handleWalletAdjustment(user, 'DEBIT');\n                                  setOpenDropdown(null);\n                                }}\n                                className=\"flex items-center gap-2 w-full px-4 py-2 text-sm text-red-300 hover:bg-slate-700\"\n                              >\n                                <Minus className=\"h-4 w-4\" />\n                                Debit Wallet\n                              </button>\n\n                              <div className=\"border-t border-slate-600 my-1\"></div>\n\n                              <button\n                                onClick={() => {\n                                  fetchUserDetails(user.id);\n                                  setOpenDropdown(null);\n                                }}\n                                className=\"flex items-center gap-2 w-full px-4 py-2 text-sm text-slate-300 hover:bg-slate-700\"\n                                disabled={userDetailsLoading}\n                              >\n                                <Eye className=\"h-4 w-4\" />\n                                {userDetailsLoading ? 'Loading...' : 'View Details'}\n                              </button>\n                            </div>\n                          </div>\n                        )}\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n\n          {/* Pagination */}\n          {totalPages > 1 && (\n            <div className=\"flex items-center justify-between mt-6\">\n              <div className=\"text-sm text-slate-400\">\n                Page {currentPage} of {totalPages}\n              </div>\n              <div className=\"flex gap-2\">\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}\n                  disabled={currentPage === 1}\n                  className=\"border-slate-600 text-slate-300 hover:bg-slate-700\"\n                >\n                  Previous\n                </Button>\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}\n                  disabled={currentPage === totalPages}\n                  className=\"border-slate-600 text-slate-300 hover:bg-slate-700\"\n                >\n                  Next\n                </Button>\n              </div>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Wallet Adjustment Modal */}\n      {showWalletModal && (\n        <Modal\n          isOpen={showWalletModal}\n          onClose={() => setShowWalletModal(false)}\n          title={`${walletAdjustment.type === 'CREDIT' ? 'Credit' : 'Debit'} User Wallet`}\n          darkMode={true}\n        >\n          <div className=\"space-y-4\">\n            <div className=\"bg-slate-700 p-4 rounded-lg\">\n              <h3 className=\"font-medium text-white mb-2\">User Information</h3>\n              <p className=\"text-slate-300 text-sm\">Name: {walletAdjustment.userName}</p>\n              <p className=\"text-slate-300 text-sm\">Email: {walletAdjustment.userEmail}</p>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                Amount (USDT)\n              </label>\n              <Input\n                type=\"number\"\n                step=\"0.01\"\n                min=\"0\"\n                value={walletAdjustment.amount}\n                onChange={(e) => setWalletAdjustment(prev => ({ ...prev, amount: e.target.value }))}\n                placeholder=\"Enter amount\"\n                className=\"bg-slate-700 border-slate-600 text-white\"\n                disabled={walletLoading}\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                Reason *\n              </label>\n              <select\n                value={walletAdjustment.reason}\n                onChange={(e) => setWalletAdjustment(prev => ({ ...prev, reason: e.target.value }))}\n                className=\"w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white rounded-lg\"\n                disabled={walletLoading}\n              >\n                <option value=\"\">Select reason</option>\n                <option value=\"Manual Adjustment\">Manual Adjustment</option>\n                <option value=\"Bonus Payment\">Bonus Payment</option>\n                <option value=\"Refund\">Refund</option>\n                <option value=\"Correction\">Balance Correction</option>\n                <option value=\"Penalty\">Penalty</option>\n                <option value=\"Promotion\">Promotional Credit</option>\n                <option value=\"Other\">Other</option>\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                Description (Optional)\n              </label>\n              <textarea\n                value={walletAdjustment.description}\n                onChange={(e) => setWalletAdjustment(prev => ({ ...prev, description: e.target.value }))}\n                placeholder=\"Additional details about this adjustment...\"\n                rows={3}\n                className=\"w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white rounded-lg resize-none\"\n                disabled={walletLoading}\n              />\n            </div>\n\n            {walletError && (\n              <div className=\"bg-red-900/20 border border-red-500 rounded-lg p-3\">\n                <p className=\"text-red-400 text-sm\">{walletError}</p>\n              </div>\n            )}\n\n            {walletSuccess && (\n              <div className=\"bg-green-900/20 border border-green-500 rounded-lg p-3\">\n                <p className=\"text-green-400 text-sm\">{walletSuccess}</p>\n              </div>\n            )}\n\n            <div className=\"flex justify-end space-x-3 pt-4\">\n              <Button\n                variant=\"outline\"\n                onClick={() => setShowWalletModal(false)}\n                disabled={walletLoading}\n                className=\"border-slate-600 text-slate-300 hover:bg-slate-700\"\n              >\n                Cancel\n              </Button>\n              <Button\n                onClick={submitWalletAdjustment}\n                disabled={walletLoading || !walletAdjustment.amount || !walletAdjustment.reason}\n                className={`${\n                  walletAdjustment.type === 'CREDIT'\n                    ? 'bg-green-600 hover:bg-green-700'\n                    : 'bg-red-600 hover:bg-red-700'\n                } text-white`}\n              >\n                {walletLoading ? 'Processing...' : `${walletAdjustment.type === 'CREDIT' ? 'Credit' : 'Debit'} Wallet`}\n              </Button>\n            </div>\n          </div>\n        </Modal>\n      )}\n\n      {/* User Details Modal */}\n      {showUserDetailsModal && selectedUserDetails && (\n        <Modal\n          isOpen={showUserDetailsModal}\n          onClose={() => setShowUserDetailsModal(false)}\n          title=\"User Details\"\n          darkMode={true}\n          size=\"xl\"\n        >\n          <div className=\"space-y-6 max-h-[80vh] overflow-y-auto\">\n            {/* Basic Information */}\n            <div className=\"bg-slate-700 p-4 rounded-lg\">\n              <h3 className=\"font-medium text-white mb-3 flex items-center gap-2\">\n                <Users className=\"h-4 w-4\" />\n                Basic Information\n              </h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"text-xs text-slate-400\">Full Name</label>\n                  <p className=\"text-white\">{selectedUserDetails.firstName} {selectedUserDetails.lastName}</p>\n                </div>\n                <div>\n                  <label className=\"text-xs text-slate-400\">Email</label>\n                  <p className=\"text-white\">{selectedUserDetails.email}</p>\n                </div>\n                <div>\n                  <label className=\"text-xs text-slate-400\">Referral ID</label>\n                  <p className=\"text-white font-mono\">{selectedUserDetails.referralId}</p>\n                </div>\n                <div>\n                  <label className=\"text-xs text-slate-400\">Role</label>\n                  <p className=\"text-white\">{getRoleBadge(selectedUserDetails.role)}</p>\n                </div>\n                <div>\n                  <label className=\"text-xs text-slate-400\">Status</label>\n                  <p className=\"text-white\">{getStatusBadge(selectedUserDetails.isActive)}</p>\n                </div>\n                <div>\n                  <label className=\"text-xs text-slate-400\">KYC Status</label>\n                  <div className=\"flex items-center gap-2\">\n                    {getKYCStatusIcon(selectedUserDetails.kycStatus)}\n                    <span className=\"text-white\">{selectedUserDetails.kycStatus}</span>\n                  </div>\n                </div>\n                <div>\n                  <label className=\"text-xs text-slate-400\">Joined Date</label>\n                  <p className=\"text-white\">{formatDateTime(selectedUserDetails.createdAt)}</p>\n                </div>\n                {selectedUserDetails.referrerId && (\n                  <div>\n                    <label className=\"text-xs text-slate-400\">Referred By</label>\n                    <p className=\"text-white font-mono\">{selectedUserDetails.referrerId}</p>\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* Wallet Information */}\n            <div className=\"bg-slate-700 p-4 rounded-lg\">\n              <h3 className=\"font-medium text-white mb-3 flex items-center gap-2\">\n                <Wallet className=\"h-4 w-4\" />\n                Wallet Information\n              </h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"text-xs text-slate-400\">Available Balance</label>\n                  <p className=\"text-white text-lg font-semibold\">{formatCurrency(selectedUserDetails.walletBalance.availableBalance)}</p>\n                </div>\n                <div>\n                  <label className=\"text-xs text-slate-400\">Total Earnings</label>\n                  <p className=\"text-green-400 text-lg font-semibold\">{formatCurrency(selectedUserDetails.walletBalance.totalEarnings)}</p>\n                </div>\n                <div>\n                  <label className=\"text-xs text-slate-400\">Total Deposits</label>\n                  <p className=\"text-blue-400\">{formatCurrency(selectedUserDetails.walletBalance.totalDeposits)}</p>\n                </div>\n                <div>\n                  <label className=\"text-xs text-slate-400\">Total Withdrawals</label>\n                  <p className=\"text-red-400\">{formatCurrency(selectedUserDetails.walletBalance.totalWithdrawals)}</p>\n                </div>\n              </div>\n            </div>\n\n            {/* Mining Units */}\n            <div className=\"bg-slate-700 p-4 rounded-lg\">\n              <h3 className=\"font-medium text-white mb-3 flex items-center gap-2\">\n                <DollarSign className=\"h-4 w-4\" />\n                Mining Units\n              </h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"text-xs text-slate-400\">Total Units</label>\n                  <p className=\"text-white text-lg font-semibold\">{selectedUserDetails.miningUnits.totalUnits}</p>\n                </div>\n                <div>\n                  <label className=\"text-xs text-slate-400\">Total Investment</label>\n                  <p className=\"text-white text-lg font-semibold\">{formatCurrency(selectedUserDetails.miningUnits.totalInvestment)}</p>\n                </div>\n                <div>\n                  <label className=\"text-xs text-slate-400\">Total TH/s</label>\n                  <p className=\"text-blue-400\">{selectedUserDetails.miningUnits.totalTHS.toFixed(2)} TH/s</p>\n                </div>\n                <div>\n                  <label className=\"text-xs text-slate-400\">Active TH/s</label>\n                  <p className=\"text-green-400\">{selectedUserDetails.miningUnits.activeTHS.toFixed(2)} TH/s</p>\n                </div>\n              </div>\n            </div>\n\n            {/* Binary Points */}\n            <div className=\"bg-slate-700 p-4 rounded-lg\">\n              <h3 className=\"font-medium text-white mb-3 flex items-center gap-2\">\n                <Shield className=\"h-4 w-4\" />\n                Binary Points\n              </h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"text-xs text-slate-400\">Left Points</label>\n                  <p className=\"text-white text-lg font-semibold\">{selectedUserDetails.binaryPoints.leftPoints}</p>\n                </div>\n                <div>\n                  <label className=\"text-xs text-slate-400\">Right Points</label>\n                  <p className=\"text-white text-lg font-semibold\">{selectedUserDetails.binaryPoints.rightPoints}</p>\n                </div>\n                <div>\n                  <label className=\"text-xs text-slate-400\">Total Matched</label>\n                  <p className=\"text-green-400\">{selectedUserDetails.binaryPoints.totalMatched}</p>\n                </div>\n                <div>\n                  <label className=\"text-xs text-slate-400\">Last Match Date</label>\n                  <p className=\"text-white\">\n                    {selectedUserDetails.binaryPoints.lastMatchDate\n                      ? formatDateTime(selectedUserDetails.binaryPoints.lastMatchDate)\n                      : 'Never'\n                    }\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            {/* Referral Statistics */}\n            <div className=\"bg-slate-700 p-4 rounded-lg\">\n              <h3 className=\"font-medium text-white mb-3 flex items-center gap-2\">\n                <Users className=\"h-4 w-4\" />\n                Referral Statistics\n              </h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"text-xs text-slate-400\">Direct Referrals</label>\n                  <p className=\"text-white text-lg font-semibold\">{selectedUserDetails.referralStats.directReferrals}</p>\n                </div>\n                <div>\n                  <label className=\"text-xs text-slate-400\">Total Team</label>\n                  <p className=\"text-white text-lg font-semibold\">{selectedUserDetails.referralStats.totalTeam}</p>\n                </div>\n                <div>\n                  <label className=\"text-xs text-slate-400\">Left Team</label>\n                  <p className=\"text-blue-400\">{selectedUserDetails.referralStats.leftTeam}</p>\n                </div>\n                <div>\n                  <label className=\"text-xs text-slate-400\">Right Team</label>\n                  <p className=\"text-green-400\">{selectedUserDetails.referralStats.rightTeam}</p>\n                </div>\n              </div>\n            </div>\n\n            {/* Recent Activity */}\n            {selectedUserDetails.recentActivity.length > 0 && (\n              <div className=\"bg-slate-700 p-4 rounded-lg\">\n                <h3 className=\"font-medium text-white mb-3 flex items-center gap-2\">\n                  <Eye className=\"h-4 w-4\" />\n                  Recent Activity\n                </h3>\n                <div className=\"space-y-2 max-h-40 overflow-y-auto\">\n                  {selectedUserDetails.recentActivity.map((activity, index) => (\n                    <div key={index} className=\"flex justify-between items-center p-2 bg-slate-600 rounded\">\n                      <div>\n                        <p className=\"text-white text-sm\">{activity.description}</p>\n                        <p className=\"text-slate-400 text-xs\">{formatDateTime(activity.date)}</p>\n                      </div>\n                      {activity.amount && (\n                        <p className=\"text-green-400 font-semibold\">{formatCurrency(activity.amount)}</p>\n                      )}\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n          </div>\n        </Modal>\n      )}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAmBA;AAxBA;;;;;;AAgGO,MAAM,iBAA2B;IACtC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiD;IAChG,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhE,0BAA0B;IAC1B,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;QACzE,QAAQ;QACR,UAAU;QACV,WAAW;QACX,QAAQ;QACR,MAAM;QACN,QAAQ;QACR,aAAa;IACf;IACA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,2BAA2B;IAC3B,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IACnF,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAa;QAAY;KAAa;IAE1C,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,gBAAgB,CAAC,AAAC,MAAM,MAAM,CAAa,OAAO,CAAC,cAAc;gBACnE,gBAAgB;YAClB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG;QAAC;KAAa;IAEjB,MAAM,aAAa;QACjB,IAAI;YACF,WAAW;YACX,MAAM,SAAS,IAAI,gBAAgB;gBACjC,MAAM,YAAY,QAAQ;gBAC1B,OAAO;gBACP,QAAQ;gBACR,QAAQ;YACV;YAEA,MAAM,WAAW,MAAM,MAAM,CAAC,iBAAiB,EAAE,QAAQ,EAAE;gBACzD,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,SAAS,KAAK,IAAI,CAAC,KAAK;oBACxB,cAAc,KAAK,IAAI,CAAC,UAAU;gBACpC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB,OAAO,QAAgB;QAC9C,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,2BAA2B;gBACtD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,aAAa;gBACb,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAQ;gBAAO;YACxC;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,cAAc,mBAAmB;YACnC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD;IACF;IAEA,MAAM,yBAAyB,CAAC,MAAY;QAC1C,oBAAoB;YAClB,QAAQ,KAAK,EAAE;YACf,UAAU,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;YAC9C,WAAW,KAAK,KAAK;YACrB,QAAQ;YACR;YACA,QAAQ;YACR,aAAa;QACf;QACA,eAAe;QACf,iBAAiB;QACjB,mBAAmB;IACrB;IAEA,MAAM,yBAAyB;QAC7B,IAAI,CAAC,iBAAiB,MAAM,IAAI,CAAC,iBAAiB,MAAM,EAAE;YACxD,eAAe;YACf;QACF;QAEA,IAAI;YACF,iBAAiB;YACjB,eAAe;YACf,iBAAiB;YAEjB,MAAM,WAAW,MAAM,MAAM,4BAA4B;gBACvD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,aAAa;gBACb,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ,iBAAiB,MAAM;oBAC/B,QAAQ,WAAW,iBAAiB,MAAM;oBAC1C,MAAM,iBAAiB,IAAI;oBAC3B,QAAQ,iBAAiB,MAAM;oBAC/B,aAAa,iBAAiB,WAAW;gBAC3C;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,iBAAiB,CAAC,OAAO,EAAE,iBAAiB,IAAI,CAAC,WAAW,GAAG,uBAAuB,CAAC;gBACvF,WAAW;oBACT,mBAAmB;oBACnB,cAAc,oBAAoB;gBACpC,GAAG;YACL,OAAO;gBACL,eAAe,KAAK,KAAK,IAAI;YAC/B;QACF,EAAE,OAAO,OAAO;YACd,eAAe;QACjB,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,sBAAsB;YACtB,MAAM,WAAW,MAAM,MAAM,CAAC,iBAAiB,EAAE,OAAO,QAAQ,CAAC,EAAE;gBACjE,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,uBAAuB,KAAK,IAAI;oBAChC,wBAAwB;gBAC1B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD,SAAU;YACR,sBAAsB;QACxB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,4MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B;gBACE,qBAAO,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;QAC7B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,qBACE,8OAAC;YAAK,WAAW,CAAC,wEAAwE,EACxF,WACI,2BACA,yBACJ;sBACC,WAAW,WAAW;;;;;;IAG7B;IAEA,MAAM,eAAe,CAAC;QACpB,qBACE,8OAAC;YAAK,WAAW,CAAC,wEAAwE,EACxF,SAAS,UACL,0BACA,0BACJ;sBACC;;;;;;IAGP;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAIvB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;sCAAgC;;;;;;sCAC9C,8OAAC;4BAAE,WAAU;sCAAsB;;;;;;;;;;;;;;;;;0BAKvC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC,iIAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;;;;;;;;;;;;;;;;0CAIhB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oCAC/C,WAAU;;sDAEV,8OAAC;4CAAO,OAAM;sDAAM;;;;;;sDACpB,8OAAC;4CAAO,OAAM;sDAAS;;;;;;sDACvB,8OAAC;4CAAO,OAAM;sDAAW;;;;;;sDACzB,8OAAC;4CAAO,OAAM;sDAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQtC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAY;gCACrB,MAAM,MAAM;gCAAC;;;;;;;;;;;;kCAGzB,8OAAC,gIAAA,CAAA,cAAW;;0CACV,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;sDACC,cAAA,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;;;;;;;;;;;;sDAG/D,8OAAC;sDACE,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;oDAAiB,WAAU;;sEAC1B,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;;kFACC,8OAAC;wEAAI,WAAU;;4EACZ,KAAK,SAAS;4EAAC;4EAAE,KAAK,QAAQ;;;;;;;kFAEjC,8OAAC;wEAAI,WAAU;kFAA0B,KAAK,KAAK;;;;;;kFACnD,8OAAC;wEAAI,WAAU;;4EAAyB;4EAAK,KAAK,UAAU;;;;;;;;;;;;;;;;;;sEAGhE,8OAAC;4DAAG,WAAU;sEACX,aAAa,KAAK,IAAI;;;;;;sEAEzB,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAI,WAAU;;oEACZ,iBAAiB,KAAK,SAAS;kFAChC,8OAAC;wEAAK,WAAU;kFAA0B,KAAK,SAAS;;;;;;;;;;;;;;;;;sEAG5D,8OAAC;4DAAG,WAAU;sEACX,eAAe,KAAK,QAAQ;;;;;;sEAE/B,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,sMAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;kFAClB,8OAAC;wEAAK,WAAU;kFACb,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,aAAa,EAAE,oBAAoB;;;;;;;;;;;;;;;;;sEAI9D,8OAAC;4DAAG,WAAU;sEACX,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,SAAS;;;;;;sEAEhC,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,kIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,SAAS,IAAM,gBAAgB,iBAAiB,KAAK,EAAE,GAAG,OAAO,KAAK,EAAE;wEACxE,WAAU;;0FAEV,8OAAC,0NAAA,CAAA,eAAY;gFAAC,WAAU;;;;;;0FACxB,8OAAC,oNAAA,CAAA,cAAW;gFAAC,WAAU;;;;;;;;;;;;oEAGxB,iBAAiB,KAAK,EAAE,kBACvB,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFACC,SAAS;wFACP,iBAAiB,KAAK,EAAE,EAAE,KAAK,QAAQ,GAAG,eAAe;wFACzD,gBAAgB;oFAClB;oFACA,WAAU;;wFAET,KAAK,QAAQ,iBAAG,8OAAC,wMAAA,CAAA,QAAK;4FAAC,WAAU;;;;;iHAAe,8OAAC,gNAAA,CAAA,YAAS;4FAAC,WAAU;;;;;;wFACrE,KAAK,QAAQ,GAAG,oBAAoB;;;;;;;gFAGtC,KAAK,IAAI,KAAK,wBACb,8OAAC;oFACC,SAAS;wFACP,iBAAiB,KAAK,EAAE,EAAE;wFAC1B,gBAAgB;oFAClB;oFACA,WAAU;;sGAEV,8OAAC,sMAAA,CAAA,SAAM;4FAAC,WAAU;;;;;;wFAAY;;;;;;;8FAKlC,8OAAC;oFAAI,WAAU;;;;;;8FAEf,8OAAC;oFACC,SAAS;wFACP,uBAAuB,MAAM;wFAC7B,gBAAgB;oFAClB;oFACA,WAAU;;sGAEV,8OAAC,kMAAA,CAAA,OAAI;4FAAC,WAAU;;;;;;wFAAY;;;;;;;8FAI9B,8OAAC;oFACC,SAAS;wFACP,uBAAuB,MAAM;wFAC7B,gBAAgB;oFAClB;oFACA,WAAU;;sGAEV,8OAAC,oMAAA,CAAA,QAAK;4FAAC,WAAU;;;;;;wFAAY;;;;;;;8FAI/B,8OAAC;oFAAI,WAAU;;;;;;8FAEf,8OAAC;oFACC,SAAS;wFACP,iBAAiB,KAAK,EAAE;wFACxB,gBAAgB;oFAClB;oFACA,WAAU;oFACV,UAAU;;sGAEV,8OAAC,gMAAA,CAAA,MAAG;4FAAC,WAAU;;;;;;wFACd,qBAAqB,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mDA3G1C,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;4BAyHvB,aAAa,mBACZ,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;4CAAyB;4CAChC;4CAAY;4CAAK;;;;;;;kDAEzB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;gDACzD,UAAU,gBAAgB;gDAC1B,WAAU;0DACX;;;;;;0DAGD,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,YAAY,OAAO;gDAClE,UAAU,gBAAgB;gDAC1B,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUV,iCACC,8OAAC,iIAAA,CAAA,QAAK;gBACJ,QAAQ;gBACR,SAAS,IAAM,mBAAmB;gBAClC,OAAO,GAAG,iBAAiB,IAAI,KAAK,WAAW,WAAW,QAAQ,YAAY,CAAC;gBAC/E,UAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,8OAAC;oCAAE,WAAU;;wCAAyB;wCAAO,iBAAiB,QAAQ;;;;;;;8CACtE,8OAAC;oCAAE,WAAU;;wCAAyB;wCAAQ,iBAAiB,SAAS;;;;;;;;;;;;;sCAG1E,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAAgD;;;;;;8CAGjE,8OAAC,iIAAA,CAAA,QAAK;oCACJ,MAAK;oCACL,MAAK;oCACL,KAAI;oCACJ,OAAO,iBAAiB,MAAM;oCAC9B,UAAU,CAAC,IAAM,oBAAoB,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCACjF,aAAY;oCACZ,WAAU;oCACV,UAAU;;;;;;;;;;;;sCAId,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAAgD;;;;;;8CAGjE,8OAAC;oCACC,OAAO,iBAAiB,MAAM;oCAC9B,UAAU,CAAC,IAAM,oBAAoB,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCACjF,WAAU;oCACV,UAAU;;sDAEV,8OAAC;4CAAO,OAAM;sDAAG;;;;;;sDACjB,8OAAC;4CAAO,OAAM;sDAAoB;;;;;;sDAClC,8OAAC;4CAAO,OAAM;sDAAgB;;;;;;sDAC9B,8OAAC;4CAAO,OAAM;sDAAS;;;;;;sDACvB,8OAAC;4CAAO,OAAM;sDAAa;;;;;;sDAC3B,8OAAC;4CAAO,OAAM;sDAAU;;;;;;sDACxB,8OAAC;4CAAO,OAAM;sDAAY;;;;;;sDAC1B,8OAAC;4CAAO,OAAM;sDAAQ;;;;;;;;;;;;;;;;;;sCAI1B,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAAgD;;;;;;8CAGjE,8OAAC;oCACC,OAAO,iBAAiB,WAAW;oCACnC,UAAU,CAAC,IAAM,oBAAoB,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCACtF,aAAY;oCACZ,MAAM;oCACN,WAAU;oCACV,UAAU;;;;;;;;;;;;wBAIb,6BACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;wBAIxC,+BACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAA0B;;;;;;;;;;;sCAI3C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,mBAAmB;oCAClC,UAAU;oCACV,WAAU;8CACX;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU,iBAAiB,CAAC,iBAAiB,MAAM,IAAI,CAAC,iBAAiB,MAAM;oCAC/E,WAAW,GACT,iBAAiB,IAAI,KAAK,WACtB,oCACA,8BACL,WAAW,CAAC;8CAEZ,gBAAgB,kBAAkB,GAAG,iBAAiB,IAAI,KAAK,WAAW,WAAW,QAAQ,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;YAQ/G,wBAAwB,qCACvB,8OAAC,iIAAA,CAAA,QAAK;gBACJ,QAAQ;gBACR,SAAS,IAAM,wBAAwB;gBACvC,OAAM;gBACN,UAAU;gBACV,MAAK;0BAEL,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAG/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAyB;;;;;;8DAC1C,8OAAC;oDAAE,WAAU;;wDAAc,oBAAoB,SAAS;wDAAC;wDAAE,oBAAoB,QAAQ;;;;;;;;;;;;;sDAEzF,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAyB;;;;;;8DAC1C,8OAAC;oDAAE,WAAU;8DAAc,oBAAoB,KAAK;;;;;;;;;;;;sDAEtD,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAyB;;;;;;8DAC1C,8OAAC;oDAAE,WAAU;8DAAwB,oBAAoB,UAAU;;;;;;;;;;;;sDAErE,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAyB;;;;;;8DAC1C,8OAAC;oDAAE,WAAU;8DAAc,aAAa,oBAAoB,IAAI;;;;;;;;;;;;sDAElE,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAyB;;;;;;8DAC1C,8OAAC;oDAAE,WAAU;8DAAc,eAAe,oBAAoB,QAAQ;;;;;;;;;;;;sDAExE,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAyB;;;;;;8DAC1C,8OAAC;oDAAI,WAAU;;wDACZ,iBAAiB,oBAAoB,SAAS;sEAC/C,8OAAC;4DAAK,WAAU;sEAAc,oBAAoB,SAAS;;;;;;;;;;;;;;;;;;sDAG/D,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAyB;;;;;;8DAC1C,8OAAC;oDAAE,WAAU;8DAAc,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,oBAAoB,SAAS;;;;;;;;;;;;wCAExE,oBAAoB,UAAU,kBAC7B,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAyB;;;;;;8DAC1C,8OAAC;oDAAE,WAAU;8DAAwB,oBAAoB,UAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAO3E,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGhC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAyB;;;;;;8DAC1C,8OAAC;oDAAE,WAAU;8DAAoC,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,oBAAoB,aAAa,CAAC,gBAAgB;;;;;;;;;;;;sDAEpH,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAyB;;;;;;8DAC1C,8OAAC;oDAAE,WAAU;8DAAwC,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,oBAAoB,aAAa,CAAC,aAAa;;;;;;;;;;;;sDAErH,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAyB;;;;;;8DAC1C,8OAAC;oDAAE,WAAU;8DAAiB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,oBAAoB,aAAa,CAAC,aAAa;;;;;;;;;;;;sDAE9F,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAyB;;;;;;8DAC1C,8OAAC;oDAAE,WAAU;8DAAgB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,oBAAoB,aAAa,CAAC,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;sCAMpG,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGpC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAyB;;;;;;8DAC1C,8OAAC;oDAAE,WAAU;8DAAoC,oBAAoB,WAAW,CAAC,UAAU;;;;;;;;;;;;sDAE7F,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAyB;;;;;;8DAC1C,8OAAC;oDAAE,WAAU;8DAAoC,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,oBAAoB,WAAW,CAAC,eAAe;;;;;;;;;;;;sDAEjH,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAyB;;;;;;8DAC1C,8OAAC;oDAAE,WAAU;;wDAAiB,oBAAoB,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC;wDAAG;;;;;;;;;;;;;sDAEpF,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAyB;;;;;;8DAC1C,8OAAC;oDAAE,WAAU;;wDAAkB,oBAAoB,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC;wDAAG;;;;;;;;;;;;;;;;;;;;;;;;;sCAM1F,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGhC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAyB;;;;;;8DAC1C,8OAAC;oDAAE,WAAU;8DAAoC,oBAAoB,YAAY,CAAC,UAAU;;;;;;;;;;;;sDAE9F,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAyB;;;;;;8DAC1C,8OAAC;oDAAE,WAAU;8DAAoC,oBAAoB,YAAY,CAAC,WAAW;;;;;;;;;;;;sDAE/F,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAyB;;;;;;8DAC1C,8OAAC;oDAAE,WAAU;8DAAkB,oBAAoB,YAAY,CAAC,YAAY;;;;;;;;;;;;sDAE9E,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAyB;;;;;;8DAC1C,8OAAC;oDAAE,WAAU;8DACV,oBAAoB,YAAY,CAAC,aAAa,GAC3C,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,oBAAoB,YAAY,CAAC,aAAa,IAC7D;;;;;;;;;;;;;;;;;;;;;;;;sCAQZ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAG/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAyB;;;;;;8DAC1C,8OAAC;oDAAE,WAAU;8DAAoC,oBAAoB,aAAa,CAAC,eAAe;;;;;;;;;;;;sDAEpG,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAyB;;;;;;8DAC1C,8OAAC;oDAAE,WAAU;8DAAoC,oBAAoB,aAAa,CAAC,SAAS;;;;;;;;;;;;sDAE9F,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAyB;;;;;;8DAC1C,8OAAC;oDAAE,WAAU;8DAAiB,oBAAoB,aAAa,CAAC,QAAQ;;;;;;;;;;;;sDAE1E,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAyB;;;;;;8DAC1C,8OAAC;oDAAE,WAAU;8DAAkB,oBAAoB,aAAa,CAAC,SAAS;;;;;;;;;;;;;;;;;;;;;;;;wBAM/E,oBAAoB,cAAc,CAAC,MAAM,GAAG,mBAC3C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAG7B,8OAAC;oCAAI,WAAU;8CACZ,oBAAoB,cAAc,CAAC,GAAG,CAAC,CAAC,UAAU,sBACjD,8OAAC;4CAAgB,WAAU;;8DACzB,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAAsB,SAAS,WAAW;;;;;;sEACvD,8OAAC;4DAAE,WAAU;sEAA0B,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,IAAI;;;;;;;;;;;;gDAEpE,SAAS,MAAM,kBACd,8OAAC;oDAAE,WAAU;8DAAgC,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,MAAM;;;;;;;2CANrE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkB9B", "debugId": null}}]}