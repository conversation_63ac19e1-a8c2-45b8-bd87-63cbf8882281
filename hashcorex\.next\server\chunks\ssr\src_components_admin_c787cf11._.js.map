{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/admin/AdminLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport Link from 'next/link';\nimport { useAuth } from '@/hooks/useAuth';\nimport { Container, Flex } from '@/components/layout';\nimport { Button } from '@/components/ui';\nimport { SolarPanel } from '@/components/icons';\nimport {\n  LayoutDashboard,\n  Users,\n  Shield,\n  CreditCard,\n  Settings,\n  FileText,\n  LogOut,\n  Menu,\n  X,\n  Bell,\n  ChevronDown,\n  User,\n  ArrowLeft,\n  DollarSign,\n  MessageCircle,\n  ArrowUpDown,\n  TrendingUp\n} from 'lucide-react';\n\ninterface AdminLayoutProps {\n  children: React.ReactNode;\n  activeTab: string;\n  onTabChange: (tab: string) => void;\n}\n\nexport const AdminLayout: React.FC<AdminLayoutProps> = ({\n  children,\n  activeTab,\n  onTabChange,\n}) => {\n  const { user, logout } = useAuth();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [userDropdownOpen, setUserDropdownOpen] = useState(false);\n  const dropdownRef = useRef<HTMLDivElement>(null);\n\n  // Admin navigation items\n  const navigationItems = [\n    { id: 'dashboard', label: 'Dashboard', icon: LayoutDashboard },\n    { id: 'users', label: 'User Management', icon: Users },\n    { id: 'kyc', label: 'KYC Review', icon: Shield },\n    { id: 'deposits', label: 'Deposits', icon: DollarSign },\n    { id: 'withdrawals', label: 'Withdrawals', icon: CreditCard },\n    { id: 'support', label: 'Support Tickets', icon: MessageCircle },\n    { id: 'binary-points', label: 'Binary Points', icon: ArrowUpDown },\n    { id: 'referral-commissions', label: 'Referral Commissions', icon: TrendingUp },\n    { id: 'settings', label: 'System Settings', icon: Settings },\n    { id: 'logs', label: 'System Logs', icon: FileText },\n  ];\n\n  const handleLogout = async () => {\n    await logout();\n  };\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setUserDropdownOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n\n  return (\n    <div className=\"min-h-screen bg-slate-900 flex\">\n      {/* Mobile sidebar overlay */}\n      {sidebarOpen && (\n        <div\n          className=\"fixed inset-0 z-40 bg-black bg-opacity-75 lg:hidden\"\n          onClick={() => setSidebarOpen(false)}\n        />\n      )}\n\n      {/* Fixed Sidebar */}\n      <aside className={`\n        fixed inset-y-0 left-0 z-50 w-64 bg-slate-800 shadow-xl border-r border-slate-700\n        transform transition-all duration-300 ease-in-out\n        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}\n      `}>\n        <div className=\"flex flex-col h-screen\">\n          {/* Logo Header */}\n          <div className=\"flex items-center justify-between h-14 px-5 border-b border-slate-700 bg-slate-800 flex-shrink-0\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                <SolarPanel className=\"h-5 w-5 text-white\" />\n              </div>\n              <span className=\"text-lg font-bold text-white\">HashCoreX</span>\n            </Link>\n            <button\n              onClick={() => setSidebarOpen(false)}\n              className=\"lg:hidden p-1.5 rounded-lg text-slate-400\"\n            >\n              <X className=\"h-4 w-4\" />\n            </button>\n          </div>\n\n          {/* Admin Badge */}\n          <div className=\"px-3 py-3 bg-red-600 border-b border-slate-700\">\n            <div className=\"flex items-center space-x-2 text-white\">\n              <Shield className=\"h-4 w-4\" />\n              <span className=\"text-sm font-semibold\">Admin Panel</span>\n            </div>\n          </div>\n\n          {/* Navigation Menu */}\n          <nav className=\"flex-1 px-3 py-4 space-y-1 min-h-0\">\n            {navigationItems.map((item) => {\n              const Icon = item.icon;\n              const isActive = activeTab === item.id;\n\n              return (\n                <button\n                  key={item.id}\n                  onClick={() => {\n                    onTabChange(item.id);\n                    setSidebarOpen(false);\n                  }}\n                  className={`\n                    w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-left group\n                    ${isActive\n                      ? 'bg-blue-600 text-white shadow-md'\n                      : 'text-slate-300'\n                    }\n                  `}\n                >\n                  <Icon className={`h-4 w-4 ${isActive ? 'text-white' : 'text-slate-400'}`} />\n                  <span className=\"font-medium text-sm\">{item.label}</span>\n                </button>\n              );\n            })}\n          </nav>\n\n          {/* Back to Dashboard */}\n          <div className=\"px-3 py-3 border-t border-slate-700\">\n            <Link\n              href=\"/dashboard\"\n              className=\"w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-slate-300 group\"\n            >\n              <ArrowLeft className=\"h-4 w-4\" />\n              <span className=\"font-medium text-sm\">Back to Dashboard</span>\n            </Link>\n          </div>\n\n          {/* Sidebar Footer */}\n          <div className=\"px-3 py-3 border-t border-slate-700 bg-slate-900 flex-shrink-0\">\n            <button\n              onClick={handleLogout}\n              className=\"w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-slate-300 group\"\n            >\n              <LogOut className=\"h-4 w-4\" />\n              <span className=\"font-medium text-sm\">Logout</span>\n            </button>\n          </div>\n        </div>\n      </aside>\n\n      {/* Main Content Area */}\n      <div className=\"flex-1 flex flex-col min-w-0 lg:ml-64\">\n        {/* Top Navigation Bar */}\n        <header className=\"bg-slate-800 shadow-sm border-b border-slate-700 sticky top-0 z-30\">\n          <div className=\"px-4 sm:px-6 lg:px-8 xl:px-12\">\n            <Flex justify=\"between\" align=\"center\" className=\"h-16\">\n              <div className=\"flex items-center space-x-4\">\n                <button\n                  onClick={() => setSidebarOpen(true)}\n                  className=\"lg:hidden p-2 rounded-lg text-slate-400\"\n                >\n                  <Menu className=\"h-6 w-6\" />\n                </button>\n                <div>\n                  <h1 className=\"text-xl font-bold text-white capitalize\">\n                    {navigationItems.find(item => item.id === activeTab)?.label || 'Admin Dashboard'}\n                  </h1>\n                  <p className=\"text-sm text-slate-400 hidden sm:block\">\n                    Manage platform operations and user activities\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"flex items-center space-x-3\">\n                {/* Notifications */}\n                <button className=\"relative p-2 rounded-lg text-slate-400\">\n                  <Bell className=\"h-5 w-5\" />\n                  <span className=\"absolute top-1 right-1 h-2 w-2 bg-orange-500 rounded-full\"></span>\n                </button>\n\n                {/* Admin Badge */}\n                <div className=\"px-3 py-1.5 rounded-lg text-xs font-semibold border bg-red-600 text-white border-red-500\">\n                  ADMIN\n                </div>\n\n                {/* User Dropdown */}\n                <div className=\"relative\" ref={dropdownRef}>\n                  <button\n                    onClick={() => setUserDropdownOpen(!userDropdownOpen)}\n                    className=\"flex items-center space-x-2 p-1 rounded-lg\"\n                  >\n                    <div className=\"w-8 h-8 bg-orange-600 rounded-lg flex items-center justify-center\">\n                      <span className=\"text-white font-semibold text-sm\">\n                        {user?.firstName?.charAt(0).toUpperCase() || user?.email.charAt(0).toUpperCase()}\n                      </span>\n                    </div>\n                    <ChevronDown className={`h-4 w-4 text-slate-400 transition-transform ${userDropdownOpen ? 'rotate-180' : ''}`} />\n                  </button>\n\n                  {/* Dropdown Menu */}\n                  {userDropdownOpen && (\n                    <div className=\"absolute right-0 mt-2 w-64 bg-slate-800 rounded-xl shadow-lg border border-slate-700 py-2 z-50\">\n                      {/* User Info */}\n                      <div className=\"px-4 py-3 border-b border-slate-700\">\n                        <div className=\"flex items-center space-x-3\">\n                          <div className=\"w-10 h-10 bg-orange-600 rounded-lg flex items-center justify-center\">\n                            <span className=\"text-white font-bold\">\n                              {user?.firstName?.charAt(0).toUpperCase() || user?.email.charAt(0).toUpperCase()}\n                            </span>\n                          </div>\n                          <div className=\"flex-1 min-w-0\">\n                            <p className=\"text-sm font-semibold text-white truncate\">\n                              {user?.firstName && user?.lastName\n                                ? `${user.firstName} ${user.lastName}`\n                                : user?.email.split('@')[0]\n                              }\n                            </p>\n                            <p className=\"text-xs text-slate-400\">\n                              ID: {user?.referralId}\n                            </p>\n                            <p className=\"text-xs text-red-400 font-medium\">\n                              Administrator\n                            </p>\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Menu Items */}\n                      <div className=\"py-1\">\n                        <Link\n                          href=\"/dashboard\"\n                          onClick={() => setUserDropdownOpen(false)}\n                          className=\"w-full flex items-center space-x-3 px-4 py-2 text-sm text-slate-300\"\n                        >\n                          <User className=\"h-4 w-4\" />\n                          <span>User Dashboard</span>\n                        </Link>\n                        <div className=\"border-t border-slate-700 my-1\"></div>\n                        <button\n                          onClick={() => {\n                            setUserDropdownOpen(false);\n                            handleLogout();\n                          }}\n                          className=\"w-full flex items-center space-x-3 px-4 py-2 text-sm text-red-400\"\n                        >\n                          <LogOut className=\"h-4 w-4\" />\n                          <span>Sign Out</span>\n                        </button>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </Flex>\n          </div>\n        </header>\n\n        {/* Main Content */}\n        <main className=\"flex-1 bg-slate-900 overflow-y-auto\">\n          <div className=\"px-4 sm:px-6 lg:px-8 xl:px-12 py-6\">\n            <div className=\"max-w-7xl mx-auto\">\n              {children}\n            </div>\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;;AAkCO,MAAM,cAA0C,CAAC,EACtD,QAAQ,EACR,SAAS,EACT,WAAW,EACZ;IACC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,yBAAyB;IACzB,MAAM,kBAAkB;QACtB;YAAE,IAAI;YAAa,OAAO;YAAa,MAAM,4NAAA,CAAA,kBAAe;QAAC;QAC7D;YAAE,IAAI;YAAS,OAAO;YAAmB,MAAM,oMAAA,CAAA,QAAK;QAAC;QACrD;YAAE,IAAI;YAAO,OAAO;YAAc,MAAM,sMAAA,CAAA,SAAM;QAAC;QAC/C;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM,kNAAA,CAAA,aAAU;QAAC;QACtD;YAAE,IAAI;YAAe,OAAO;YAAe,MAAM,kNAAA,CAAA,aAAU;QAAC;QAC5D;YAAE,IAAI;YAAW,OAAO;YAAmB,MAAM,wNAAA,CAAA,gBAAa;QAAC;QAC/D;YAAE,IAAI;YAAiB,OAAO;YAAiB,MAAM,wNAAA,CAAA,cAAW;QAAC;QACjE;YAAE,IAAI;YAAwB,OAAO;YAAwB,MAAM,kNAAA,CAAA,aAAU;QAAC;QAC9E;YAAE,IAAI;YAAY,OAAO;YAAmB,MAAM,0MAAA,CAAA,WAAQ;QAAC;QAC3D;YAAE,IAAI;YAAQ,OAAO;YAAe,MAAM,8MAAA,CAAA,WAAQ;QAAC;KACpD;IAED,MAAM,eAAe;QACnB,MAAM;IACR;IAEA,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9E,oBAAoB;YACtB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;;YAEZ,6BACC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,eAAe;;;;;;0BAKlC,8OAAC;gBAAM,WAAW,CAAC;;;QAGjB,EAAE,cAAc,kBAAkB,qCAAqC;MACzE,CAAC;0BACC,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,yIAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;sDAExB,8OAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;;8CAEjD,8OAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKjB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;;;;;;;;;;;;sCAK5C,8OAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC;gCACpB,MAAM,OAAO,KAAK,IAAI;gCACtB,MAAM,WAAW,cAAc,KAAK,EAAE;gCAEtC,qBACE,8OAAC;oCAEC,SAAS;wCACP,YAAY,KAAK,EAAE;wCACnB,eAAe;oCACjB;oCACA,WAAW,CAAC;;oBAEV,EAAE,WACE,qCACA,iBACH;kBACH,CAAC;;sDAED,8OAAC;4CAAK,WAAW,CAAC,QAAQ,EAAE,WAAW,eAAe,kBAAkB;;;;;;sDACxE,8OAAC;4CAAK,WAAU;sDAAuB,KAAK,KAAK;;;;;;;mCAd5C,KAAK,EAAE;;;;;4BAiBlB;;;;;;sCAIF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,8OAAC;wCAAK,WAAU;kDAAsB;;;;;;;;;;;;;;;;;sCAK1C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCAAK,WAAU;kDAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO9C,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAO,WAAU;kCAChB,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,oIAAA,CAAA,OAAI;gCAAC,SAAQ;gCAAU,OAAM;gCAAS,WAAU;;kDAC/C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS,IAAM,eAAe;gDAC9B,WAAU;0DAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEACX,gBAAgB,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,YAAY,SAAS;;;;;;kEAEjE,8OAAC;wDAAE,WAAU;kEAAyC;;;;;;;;;;;;;;;;;;kDAM1D,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAO,WAAU;;kEAChB,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;wDAAK,WAAU;;;;;;;;;;;;0DAIlB,8OAAC;gDAAI,WAAU;0DAA2F;;;;;;0DAK1G,8OAAC;gDAAI,WAAU;gDAAW,KAAK;;kEAC7B,8OAAC;wDACC,SAAS,IAAM,oBAAoB,CAAC;wDACpC,WAAU;;0EAEV,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAK,WAAU;8EACb,MAAM,WAAW,OAAO,GAAG,iBAAiB,MAAM,MAAM,OAAO,GAAG;;;;;;;;;;;0EAGvE,8OAAC,oNAAA,CAAA,cAAW;gEAAC,WAAW,CAAC,4CAA4C,EAAE,mBAAmB,eAAe,IAAI;;;;;;;;;;;;oDAI9G,kCACC,8OAAC;wDAAI,WAAU;;0EAEb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;sFACb,cAAA,8OAAC;gFAAK,WAAU;0FACb,MAAM,WAAW,OAAO,GAAG,iBAAiB,MAAM,MAAM,OAAO,GAAG;;;;;;;;;;;sFAGvE,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAE,WAAU;8FACV,MAAM,aAAa,MAAM,WACtB,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE,GACpC,MAAM,MAAM,MAAM,IAAI,CAAC,EAAE;;;;;;8FAG/B,8OAAC;oFAAE,WAAU;;wFAAyB;wFAC/B,MAAM;;;;;;;8FAEb,8OAAC;oFAAE,WAAU;8FAAmC;;;;;;;;;;;;;;;;;;;;;;;0EAQtD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,4JAAA,CAAA,UAAI;wEACH,MAAK;wEACL,SAAS,IAAM,oBAAoB;wEACnC,WAAU;;0FAEV,8OAAC,kMAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;0FAChB,8OAAC;0FAAK;;;;;;;;;;;;kFAER,8OAAC;wEAAI,WAAU;;;;;;kFACf,8OAAC;wEACC,SAAS;4EACP,oBAAoB;4EACpB;wEACF;wEACA,WAAU;;0FAEV,8OAAC,0MAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;0FAClB,8OAAC;0FAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAYxB,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 683, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/admin/AdminDashboard.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Card, CardHeader, CardTitle, CardContent, useConfirmDialog } from '@/components/ui';\nimport { Grid } from '@/components/layout';\nimport {\n  Users,\n  DollarSign,\n  TrendingUp,\n  Zap,\n  Shield,\n  CreditCard,\n  AlertTriangle,\n  CheckCircle,\n  Play\n} from 'lucide-react';\nimport { formatCurrency, formatNumber, formatTHS } from '@/lib/utils';\n\ninterface AdminStats {\n  totalUsers: number;\n  activeUsers: number;\n  pendingKYC: number;\n  approvedKYC: number;\n  totalInvestments: number;\n  totalEarningsDistributed: number;\n  totalTHSSold: number;\n  activeTHS: number;\n  pendingWithdrawals: number;\n  totalWithdrawals: number;\n  platformRevenue: number;\n}\n\ninterface AdminDashboardProps {\n  onTabChange?: (tab: string) => void;\n}\n\nexport const AdminDashboard: React.FC<AdminDashboardProps> = ({ onTabChange }) => {\n  const [stats, setStats] = useState<AdminStats | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [processingMatching, setProcessingMatching] = useState(false);\n  const { showConfirm, ConfirmDialog } = useConfirmDialog();\n\n  useEffect(() => {\n    fetchAdminStats();\n  }, []);\n\n  const fetchAdminStats = async () => {\n    try {\n      const response = await fetch('/api/admin/stats', {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setStats(data.data);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch admin stats:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleManualBinaryMatching = () => {\n    showConfirm({\n      title: 'Manual Binary Matching',\n      message: 'Are you sure you want to manually trigger binary matching? This will process all users with binary points and distribute earnings.',\n      variant: 'warning',\n      confirmText: 'Process Matching',\n      darkMode: true,\n      onConfirm: async () => {\n        setProcessingMatching(true);\n        try {\n          const response = await fetch('/api/admin/binary-matching/manual', {\n            method: 'POST',\n            credentials: 'include',\n          });\n\n          const data = await response.json();\n\n          if (data.success) {\n            showConfirm({\n              title: 'Binary Matching Completed',\n              message: `Binary matching completed successfully!\\n\\nUsers processed: ${data.data.usersProcessed}\\nTotal payouts: $${data.data.totalPayouts.toFixed(2)}`,\n              variant: 'success',\n              confirmText: 'OK',\n              cancelText: '',\n              darkMode: true,\n              onConfirm: () => {},\n            });\n            // Refresh stats to show updated data\n            fetchAdminStats();\n          } else {\n            showConfirm({\n              title: 'Error',\n              message: `Error: ${data.error}`,\n              variant: 'danger',\n              confirmText: 'OK',\n              cancelText: '',\n              darkMode: true,\n              onConfirm: () => {},\n            });\n          }\n        } catch (error) {\n          console.error('Manual binary matching error:', error);\n          showConfirm({\n            title: 'Error',\n            message: 'Failed to process binary matching. Please try again.',\n            variant: 'danger',\n            confirmText: 'OK',\n            cancelText: '',\n            darkMode: true,\n            onConfirm: () => {},\n          });\n        } finally {\n          setProcessingMatching(false);\n        }\n      },\n    });\n  };\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        {Array.from({ length: 4 }).map((_, i) => (\n          <div key={i} className=\"animate-pulse\">\n            <div className=\"h-32 bg-slate-700 rounded-xl\"></div>\n          </div>\n        ))}\n      </div>\n    );\n  }\n\n  if (!stats) {\n    return (\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardContent className=\"text-center py-8\">\n          <p className=\"text-slate-400\">Failed to load admin statistics</p>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Welcome Section */}\n      <div className=\"bg-slate-800 border border-slate-700 rounded-xl p-6 text-white\">\n        <h1 className=\"text-2xl font-bold mb-2\">Admin Dashboard</h1>\n        <p className=\"text-slate-300\">\n          Monitor platform performance, manage users, and oversee all operations.\n        </p>\n      </div>\n\n      {/* User Statistics */}\n      <div>\n        <h2 className=\"text-lg font-semibold text-white mb-4\">User Management</h2>\n        <Grid cols={{ default: 1, md: 2, lg: 4 }} gap={6}>\n          <Card className=\"bg-slate-800 border-slate-700\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-slate-400\">Total Users</p>\n                  <p className=\"text-2xl font-bold text-blue-400\">\n                    {formatNumber(stats.totalUsers, 0)}\n                  </p>\n                </div>\n                <div className=\"h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center\">\n                  <Users className=\"h-6 w-6 text-white\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"bg-slate-800 border-slate-700\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-slate-400\">Active Users</p>\n                  <p className=\"text-2xl font-bold text-blue-400\">\n                    {formatNumber(stats.activeUsers, 0)}\n                  </p>\n                </div>\n                <div className=\"h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center\">\n                  <CheckCircle className=\"h-6 w-6 text-white\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"bg-slate-800 border-slate-700\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-slate-400\">Pending KYC</p>\n                  <p className=\"text-2xl font-bold text-orange-400\">\n                    {formatNumber(stats.pendingKYC, 0)}\n                  </p>\n                </div>\n                <div className=\"h-12 w-12 bg-orange-600 rounded-full flex items-center justify-center\">\n                  <Shield className=\"h-6 w-6 text-white\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"bg-slate-800 border-slate-700\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-slate-400\">Approved KYC</p>\n                  <p className=\"text-2xl font-bold text-blue-400\">\n                    {formatNumber(stats.approvedKYC, 0)}\n                  </p>\n                </div>\n                <div className=\"h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center\">\n                  <Shield className=\"h-6 w-6 text-white\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </Grid>\n      </div>\n\n      {/* Financial Statistics */}\n      <div>\n        <h2 className=\"text-lg font-semibold text-white mb-4\">Financial Overview</h2>\n        <Grid cols={{ default: 1, md: 2, lg: 4 }} gap={6}>\n          <Card className=\"bg-slate-800 border-slate-700\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-slate-400\">Total Investments</p>\n                  <p className=\"text-2xl font-bold text-blue-400\">\n                    {formatCurrency(stats.totalInvestments)}\n                  </p>\n                </div>\n                <div className=\"h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center\">\n                  <DollarSign className=\"h-6 w-6 text-white\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"bg-slate-800 border-slate-700\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-slate-400\">Earnings Distributed</p>\n                  <p className=\"text-2xl font-bold text-blue-400\">\n                    {formatCurrency(stats.totalEarningsDistributed)}\n                  </p>\n                </div>\n                <div className=\"h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center\">\n                  <TrendingUp className=\"h-6 w-6 text-white\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"bg-slate-800 border-slate-700\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-slate-400\">Platform Revenue</p>\n                  <p className=\"text-2xl font-bold text-orange-400\">\n                    {formatCurrency(stats.platformRevenue)}\n                  </p>\n                </div>\n                <div className=\"h-12 w-12 bg-orange-600 rounded-full flex items-center justify-center\">\n                  <DollarSign className=\"h-6 w-6 text-white\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n\n        </Grid>\n      </div>\n\n      {/* Mining Statistics */}\n      <div>\n        <h2 className=\"text-lg font-semibold text-white mb-4\">Mining Operations</h2>\n        <Grid cols={{ default: 1, md: 2 }} gap={6}>\n          <Card className=\"bg-slate-800 border-slate-700\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-slate-400\">Total TH/s Sold</p>\n                  <p className=\"text-2xl font-bold text-orange-400\">\n                    {formatTHS(stats.totalTHSSold)}\n                  </p>\n                </div>\n                <div className=\"h-12 w-12 bg-orange-600 rounded-full flex items-center justify-center\">\n                  <Zap className=\"h-6 w-6 text-white\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"bg-slate-800 border-slate-700\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-slate-400\">Active TH/s</p>\n                  <p className=\"text-2xl font-bold text-blue-400\">\n                    {formatTHS(stats.activeTHS)}\n                  </p>\n                </div>\n                <div className=\"h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center\">\n                  <Zap className=\"h-6 w-6 text-white\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </Grid>\n      </div>\n\n      {/* Withdrawal Management */}\n      <div>\n        <h2 className=\"text-lg font-semibold text-white mb-4\">Withdrawal Management</h2>\n        <Grid cols={{ default: 1, md: 2 }} gap={6}>\n          <Card className=\"bg-slate-800 border-slate-700\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-slate-400\">Pending Withdrawals</p>\n                  <p className=\"text-2xl font-bold text-red-400\">\n                    {formatNumber(stats.pendingWithdrawals, 0)}\n                  </p>\n                </div>\n                <div className=\"h-12 w-12 bg-red-600 rounded-full flex items-center justify-center\">\n                  <AlertTriangle className=\"h-6 w-6 text-white\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"bg-slate-800 border-slate-700\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-slate-400\">Total Withdrawals</p>\n                  <p className=\"text-2xl font-bold text-blue-400\">\n                    {formatCurrency(stats.totalWithdrawals)}\n                  </p>\n                </div>\n                <div className=\"h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center\">\n                  <CreditCard className=\"h-6 w-6 text-white\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </Grid>\n      </div>\n\n      {/* Quick Actions */}\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardHeader>\n          <CardTitle className=\"text-white\">Quick Actions</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <Grid cols={{ default: 1, md: 3 }} gap={4}>\n            <div\n              className=\"p-4 border border-slate-600 rounded-lg cursor-pointer\"\n              onClick={() => onTabChange?.('kyc')}\n            >\n              <div className=\"flex items-center space-x-3\">\n                <Shield className=\"h-8 w-8 text-orange-400\" />\n                <div>\n                  <h3 className=\"font-medium text-white\">Review KYC</h3>\n                  <p className=\"text-sm text-slate-400\">{stats.pendingKYC} pending reviews</p>\n                </div>\n              </div>\n            </div>\n\n            <div\n              className=\"p-4 border border-slate-600 rounded-lg cursor-pointer\"\n              onClick={() => onTabChange?.('withdrawals')}\n            >\n              <div className=\"flex items-center space-x-3\">\n                <CreditCard className=\"h-8 w-8 text-red-400\" />\n                <div>\n                  <h3 className=\"font-medium text-white\">Process Withdrawals</h3>\n                  <p className=\"text-sm text-slate-400\">{stats.pendingWithdrawals} pending</p>\n                </div>\n              </div>\n            </div>\n\n            <div\n              className=\"p-4 border border-slate-600 rounded-lg cursor-pointer\"\n              onClick={() => onTabChange?.('users')}\n            >\n              <div className=\"flex items-center space-x-3\">\n                <Users className=\"h-8 w-8 text-blue-400\" />\n                <div>\n                  <h3 className=\"font-medium text-white\">Manage Users</h3>\n                  <p className=\"text-sm text-slate-400\">{stats.totalUsers} total users</p>\n                </div>\n              </div>\n            </div>\n\n            <div\n              className={`p-4 border border-slate-600 rounded-lg cursor-pointer ${processingMatching ? 'opacity-50 cursor-not-allowed' : ''}`}\n              onClick={processingMatching ? undefined : handleManualBinaryMatching}\n            >\n              <div className=\"flex items-center space-x-3\">\n                <Play className=\"h-8 w-8 text-green-400\" />\n                <div>\n                  <h3 className=\"font-medium text-white\">\n                    {processingMatching ? 'Processing...' : 'Manual Binary Matching'}\n                  </h3>\n                  <p className=\"text-sm text-slate-400\">\n                    {processingMatching ? 'Please wait...' : 'Trigger binary matching manually'}\n                  </p>\n                </div>\n              </div>\n            </div>\n          </Grid>\n        </CardContent>\n      </Card>\n\n      {/* Confirmation Dialog */}\n      <ConfirmDialog />\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAhBA;;;;;;;AAoCO,MAAM,iBAAgD,CAAC,EAAE,WAAW,EAAE;IAC3E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,mBAAgB,AAAD;IAEtD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,SAAS,KAAK,IAAI;gBACpB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,6BAA6B;QACjC,YAAY;YACV,OAAO;YACP,SAAS;YACT,SAAS;YACT,aAAa;YACb,UAAU;YACV,WAAW;gBACT,sBAAsB;gBACtB,IAAI;oBACF,MAAM,WAAW,MAAM,MAAM,qCAAqC;wBAChE,QAAQ;wBACR,aAAa;oBACf;oBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;oBAEhC,IAAI,KAAK,OAAO,EAAE;wBAChB,YAAY;4BACV,OAAO;4BACP,SAAS,CAAC,4DAA4D,EAAE,KAAK,IAAI,CAAC,cAAc,CAAC,kBAAkB,EAAE,KAAK,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI;4BACxJ,SAAS;4BACT,aAAa;4BACb,YAAY;4BACZ,UAAU;4BACV,WAAW,KAAO;wBACpB;wBACA,qCAAqC;wBACrC;oBACF,OAAO;wBACL,YAAY;4BACV,OAAO;4BACP,SAAS,CAAC,OAAO,EAAE,KAAK,KAAK,EAAE;4BAC/B,SAAS;4BACT,aAAa;4BACb,YAAY;4BACZ,UAAU;4BACV,WAAW,KAAO;wBACpB;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,iCAAiC;oBAC/C,YAAY;wBACV,OAAO;wBACP,SAAS;wBACT,SAAS;wBACT,aAAa;wBACb,YAAY;wBACZ,UAAU;wBACV,WAAW,KAAO;oBACpB;gBACF,SAAU;oBACR,sBAAsB;gBACxB;YACF;QACF;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACZ,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,8OAAC;oBAAY,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;;;;;mBADP;;;;;;;;;;IAMlB;IAEA,IAAI,CAAC,OAAO;QACV,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;sBACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,8OAAC;oBAAE,WAAU;8BAAiB;;;;;;;;;;;;;;;;IAItC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,8OAAC;wBAAE,WAAU;kCAAiB;;;;;;;;;;;;0BAMhC,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC,oIAAA,CAAA,OAAI;wBAAC,MAAM;4BAAE,SAAS;4BAAG,IAAI;4BAAG,IAAI;wBAAE;wBAAG,KAAK;;0CAC7C,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAqC;;;;;;kEAClD,8OAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,MAAM,UAAU,EAAE;;;;;;;;;;;;0DAGpC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMzB,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAqC;;;;;;kEAClD,8OAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,MAAM,WAAW,EAAE;;;;;;;;;;;;0DAGrC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM/B,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAqC;;;;;;kEAClD,8OAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,MAAM,UAAU,EAAE;;;;;;;;;;;;0DAGpC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM1B,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAqC;;;;;;kEAClD,8OAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,MAAM,WAAW,EAAE;;;;;;;;;;;;0DAGrC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS9B,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC,oIAAA,CAAA,OAAI;wBAAC,MAAM;4BAAE,SAAS;4BAAG,IAAI;4BAAG,IAAI;wBAAE;wBAAG,KAAK;;0CAC7C,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAqC;;;;;;kEAClD,8OAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,gBAAgB;;;;;;;;;;;;0DAG1C,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM9B,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAqC;;;;;;kEAClD,8OAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,wBAAwB;;;;;;;;;;;;0DAGlD,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM9B,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAqC;;;;;;kEAClD,8OAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,eAAe;;;;;;;;;;;;0DAGzC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWlC,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC,oIAAA,CAAA,OAAI;wBAAC,MAAM;4BAAE,SAAS;4BAAG,IAAI;wBAAE;wBAAG,KAAK;;0CACtC,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAqC;;;;;;kEAClD,8OAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,mHAAA,CAAA,YAAS,AAAD,EAAE,MAAM,YAAY;;;;;;;;;;;;0DAGjC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMvB,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAqC;;;;;;kEAClD,8OAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,mHAAA,CAAA,YAAS,AAAD,EAAE,MAAM,SAAS;;;;;;;;;;;;0DAG9B,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS3B,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC,oIAAA,CAAA,OAAI;wBAAC,MAAM;4BAAE,SAAS;4BAAG,IAAI;wBAAE;wBAAG,KAAK;;0CACtC,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAqC;;;;;;kEAClD,8OAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,MAAM,kBAAkB,EAAE;;;;;;;;;;;;0DAG5C,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMjC,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAqC;;;;;;kEAClD,8OAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,gBAAgB;;;;;;;;;;;;0DAG1C,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASlC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAa;;;;;;;;;;;kCAEpC,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC,oIAAA,CAAA,OAAI;4BAAC,MAAM;gCAAE,SAAS;gCAAG,IAAI;4BAAE;4BAAG,KAAK;;8CACtC,8OAAC;oCACC,WAAU;oCACV,SAAS,IAAM,cAAc;8CAE7B,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAyB;;;;;;kEACvC,8OAAC;wDAAE,WAAU;;4DAA0B,MAAM,UAAU;4DAAC;;;;;;;;;;;;;;;;;;;;;;;;8CAK9D,8OAAC;oCACC,WAAU;oCACV,SAAS,IAAM,cAAc;8CAE7B,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAyB;;;;;;kEACvC,8OAAC;wDAAE,WAAU;;4DAA0B,MAAM,kBAAkB;4DAAC;;;;;;;;;;;;;;;;;;;;;;;;8CAKtE,8OAAC;oCACC,WAAU;oCACV,SAAS,IAAM,cAAc;8CAE7B,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAyB;;;;;;kEACvC,8OAAC;wDAAE,WAAU;;4DAA0B,MAAM,UAAU;4DAAC;;;;;;;;;;;;;;;;;;;;;;;;8CAK9D,8OAAC;oCACC,WAAW,CAAC,sDAAsD,EAAE,qBAAqB,kCAAkC,IAAI;oCAC/H,SAAS,qBAAqB,YAAY;8CAE1C,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEACX,qBAAqB,kBAAkB;;;;;;kEAE1C,8OAAC;wDAAE,WAAU;kEACV,qBAAqB,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUvD,8OAAC;;;;;;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 1917, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/admin/KYCReview.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent, Button, Input } from '@/components/ui';\nimport {\n  Shield,\n  Eye,\n  Check,\n  X,\n  Download,\n  FileText,\n  User,\n  Calendar,\n  AlertTriangle,\n  Search,\n  Filter,\n  RefreshCw,\n  ZoomIn\n} from 'lucide-react';\nimport { formatDate } from '@/lib/utils';\n\ninterface KYCDocument {\n  id: string;\n  userId: string;\n  user: {\n    firstName: string;\n    lastName: string;\n    email: string;\n    referralId: string;\n  };\n  documentType: string;\n  documentUrl: string;\n  status: 'PENDING' | 'APPROVED' | 'REJECTED';\n  submittedAt: string;\n  reviewedAt?: string;\n  rejectionReason?: string;\n}\n\ninterface KYCUser {\n  userId: string;\n  user: {\n    firstName: string;\n    lastName: string;\n    email: string;\n    referralId: string;\n  };\n  documents: KYCDocument[];\n  status: 'PENDING' | 'APPROVED' | 'REJECTED';\n  submittedAt: string;\n}\n\nexport const KYCReview: React.FC = () => {\n  const [users, setUsers] = useState<KYCUser[]>([]);\n  const [filteredUsers, setFilteredUsers] = useState<KYCUser[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [selectedUser, setSelectedUser] = useState<KYCUser | null>(null);\n  const [selectedDocument, setSelectedDocument] = useState<KYCDocument | null>(null);\n  const [reviewAction, setReviewAction] = useState<'approve' | 'reject' | null>(null);\n  const [rejectionReason, setRejectionReason] = useState('');\n  const [processing, setProcessing] = useState(false);\n  const [documentViewerOpen, setDocumentViewerOpen] = useState(false);\n\n  // Search and filter state\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState<'ALL' | 'PENDING' | 'APPROVED' | 'REJECTED'>('ALL');\n\n  useEffect(() => {\n    fetchKYCData();\n  }, []);\n\n  useEffect(() => {\n    filterUsers();\n  }, [users, searchTerm, statusFilter]);\n\n  const fetchKYCData = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch('/api/admin/kyc/all', {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setUsers(data.data);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch KYC data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const filterUsers = () => {\n    let filtered = users;\n\n    // Apply search filter\n    if (searchTerm) {\n      filtered = filtered.filter(user =>\n        user.user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        user.user.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        user.user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        user.user.referralId.toLowerCase().includes(searchTerm.toLowerCase())\n      );\n    }\n\n    // Apply status filter\n    if (statusFilter !== 'ALL') {\n      filtered = filtered.filter(user => user.status === statusFilter);\n    }\n\n    setFilteredUsers(filtered);\n  };\n\n  const handleReview = async (userId: string, action: 'approve' | 'reject', reason?: string) => {\n    try {\n      setProcessing(true);\n      const response = await fetch('/api/admin/kyc/review', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include',\n        body: JSON.stringify({\n          userId,\n          action: action.toUpperCase(),\n          rejectionReason: reason,\n        }),\n      });\n\n      if (response.ok) {\n        fetchKYCData(); // Refresh the list\n        setSelectedUser(null);\n        setSelectedDocument(null);\n        setReviewAction(null);\n        setRejectionReason('');\n      }\n    } catch (error) {\n      console.error('Failed to review KYC document:', error);\n    } finally {\n      setProcessing(false);\n    }\n  };\n\n  const getStatusBadge = (status: string) => {\n    const colors = {\n      PENDING: 'bg-yellow-900 text-yellow-300 border border-yellow-700',\n      APPROVED: 'bg-green-900 text-green-300 border border-green-700',\n      REJECTED: 'bg-red-900 text-red-300 border border-red-700',\n    };\n\n    return (\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colors[status as keyof typeof colors]}`}>\n        {status}\n      </span>\n    );\n  };\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-8 bg-slate-700 rounded w-1/4 mb-4\"></div>\n          <div className=\"h-64 bg-slate-700 rounded\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-white\">KYC Review</h1>\n          <p className=\"text-slate-400 mt-1\">Review and approve user KYC documents</p>\n        </div>\n        <div className=\"flex items-center gap-4\">\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={fetchKYCData}\n            disabled={loading}\n            className=\"border-slate-600 text-slate-300\"\n          >\n            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />\n            Refresh\n          </Button>\n          <div className=\"flex items-center gap-2 text-sm text-slate-400\">\n            <AlertTriangle className=\"h-4 w-4 text-orange-400\" />\n            {filteredUsers.filter(u => u.status === 'PENDING').length} pending reviews\n          </div>\n        </div>\n      </div>\n\n      {/* Search and Filters */}\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardContent className=\"p-6\">\n          <div className=\"flex flex-col md:flex-row gap-4\">\n            {/* Search */}\n            <div className=\"flex-1\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400\" />\n                <Input\n                  placeholder=\"Search by name, email, or user ID...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400\"\n                />\n              </div>\n            </div>\n\n            {/* Status Filter */}\n            <div className=\"md:w-48\">\n              <select\n                value={statusFilter}\n                onChange={(e) => setStatusFilter(e.target.value as any)}\n                className=\"w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                <option value=\"ALL\">All Status</option>\n                <option value=\"PENDING\">Pending</option>\n                <option value=\"APPROVED\">Approved</option>\n                <option value=\"REJECTED\">Rejected</option>\n              </select>\n            </div>\n          </div>\n\n          {/* Results Summary */}\n          <div className=\"mt-4 flex items-center gap-4 text-sm text-slate-400\">\n            <span>Showing {filteredUsers.length} of {users.length} users</span>\n            <span>•</span>\n            <span>{filteredUsers.filter(u => u.status === 'PENDING').length} pending</span>\n            <span>•</span>\n            <span>{filteredUsers.filter(u => u.status === 'APPROVED').length} approved</span>\n            <span>•</span>\n            <span>{filteredUsers.filter(u => u.status === 'REJECTED').length} rejected</span>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* KYC Users */}\n      <div className=\"grid gap-6\">\n        {filteredUsers.length === 0 ? (\n          <Card className=\"bg-slate-800 border-slate-700\">\n            <CardContent className=\"p-12 text-center\">\n              <Shield className=\"h-12 w-12 text-slate-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium text-white mb-2\">\n                {users.length === 0 ? 'No KYC Submissions' : 'No Results Found'}\n              </h3>\n              <p className=\"text-slate-400\">\n                {users.length === 0\n                  ? 'No users have submitted KYC documents yet.'\n                  : 'Try adjusting your search or filter criteria.'\n                }\n              </p>\n            </CardContent>\n          </Card>\n        ) : (\n          filteredUsers.map((userKyc) => (\n            <Card key={userKyc.userId} className=\"bg-slate-800 border-slate-700\">\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center gap-3 mb-3\">\n                      <div className=\"flex items-center gap-2\">\n                        <User className=\"h-5 w-5 text-slate-400\" />\n                        <span className=\"font-medium text-white\">\n                          {userKyc.user.firstName} {userKyc.user.lastName}\n                        </span>\n                      </div>\n                      {getStatusBadge(userKyc.status)}\n                    </div>\n\n                    <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-slate-400 mb-4\">\n                      <div>\n                        <span className=\"font-medium text-slate-300\">Email:</span> {userKyc.user.email}\n                      </div>\n                      <div>\n                        <span className=\"font-medium text-slate-300\">User ID:</span> {userKyc.user.referralId}\n                      </div>\n                      <div className=\"flex items-center gap-1\">\n                        <Calendar className=\"h-4 w-4\" />\n                        <span className=\"font-medium text-slate-300\">Submitted:</span> {formatDate(userKyc.submittedAt)}\n                      </div>\n                    </div>\n\n                    <div className=\"flex items-center gap-2 text-sm text-slate-400 mb-4\">\n                      <FileText className=\"h-4 w-4\" />\n                      <span className=\"font-medium text-slate-300\">Documents:</span> {userKyc.documents.length} uploaded\n                    </div>\n\n                    {/* Document List */}\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 mb-4\">\n                      {userKyc.documents.map((doc) => (\n                        <div key={doc.id} className=\"bg-slate-700 rounded-lg p-3\">\n                          <div className=\"flex items-center justify-between mb-2\">\n                            <span className=\"text-xs font-medium text-slate-300\">\n                              {doc.documentType === 'SELFIE' ? 'Selfie' :\n                               `${doc.idType} - ${doc.documentSide}`}\n                            </span>\n                            {getStatusBadge(doc.status)}\n                          </div>\n                          <div className=\"flex items-center gap-2\">\n                            <Button\n                              variant=\"outline\"\n                              size=\"sm\"\n                              onClick={() => {\n                                setSelectedDocument(doc);\n                                setDocumentViewerOpen(true);\n                              }}\n                              className=\"border-slate-600 text-slate-300 hover:bg-slate-600 hover:text-white text-xs px-2 py-1\"\n                            >\n                              <ZoomIn className=\"h-3 w-3 mr-1\" />\n                              View\n                            </Button>\n                          </div>\n                          {doc.rejectionReason && (\n                            <p className=\"text-xs text-red-400 mt-2 truncate\" title={doc.rejectionReason}>\n                              {doc.rejectionReason}\n                            </p>\n                          )}\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n\n                  <div className=\"flex flex-col gap-2 ml-4\">\n                    {userKyc.status === 'PENDING' && (\n                      <>\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={() => {\n                            setSelectedUser(userKyc);\n                            setReviewAction('approve');\n                          }}\n                          className=\"border-green-600 text-green-400 hover:text-green-300 hover:bg-green-900/20\"\n                        >\n                          <Check className=\"h-4 w-4 mr-1\" />\n                          Approve\n                        </Button>\n\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={() => {\n                            setSelectedUser(userKyc);\n                            setReviewAction('reject');\n                          }}\n                          className=\"border-red-600 text-red-400 hover:text-red-300 hover:bg-red-900/20\"\n                        >\n                          <X className=\"h-4 w-4 mr-1\" />\n                          Reject\n                        </Button>\n                      </>\n                    )}\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          ))\n        )}\n      </div>\n\n      {/* Document Viewer Modal */}\n      {documentViewerOpen && selectedDocument && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center p-4 z-50\">\n          <div className=\"bg-slate-800 border border-slate-700 rounded-xl max-w-4xl w-full max-h-[90vh] overflow-hidden\">\n            <div className=\"flex items-center justify-between p-4 border-b border-slate-700\">\n              <h3 className=\"text-lg font-semibold text-white\">\n                Document Viewer - {selectedDocument.documentType === 'SELFIE' ? 'Selfie' :\n                `${selectedDocument.idType} ${selectedDocument.documentSide}`}\n              </h3>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => {\n                  setDocumentViewerOpen(false);\n                  setSelectedDocument(null);\n                }}\n                className=\"border-slate-600 text-slate-300\"\n              >\n                <X className=\"h-4 w-4\" />\n              </Button>\n            </div>\n            <div className=\"p-4 max-h-[calc(90vh-120px)] overflow-auto\">\n              <img\n                src={selectedDocument.documentUrl}\n                alt=\"KYC Document\"\n                className=\"w-full h-auto max-h-[70vh] object-contain rounded-lg\"\n              />\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Review Modal */}\n      {selectedUser && reviewAction && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center p-4 z-50\">\n          <div className=\"bg-slate-800 border border-slate-700 rounded-xl max-w-md w-full p-6\">\n            <h3 className=\"text-lg font-semibold mb-4 text-white\">\n              {reviewAction === 'approve' ? 'Approve KYC Submission' : 'Reject KYC Submission'}\n            </h3>\n\n            <div className=\"mb-4\">\n              <p className=\"text-slate-400 mb-2\">\n                User: <span className=\"font-medium text-white\">{selectedUser.user.firstName} {selectedUser.user.lastName}</span>\n              </p>\n              <p className=\"text-slate-400 mb-2\">\n                Email: <span className=\"font-medium text-white\">{selectedUser.user.email}</span>\n              </p>\n              <p className=\"text-slate-400\">\n                Documents: <span className=\"font-medium text-white\">{selectedUser.documents.length} uploaded</span>\n              </p>\n            </div>\n\n            {reviewAction === 'reject' && (\n              <div className=\"mb-4\">\n                <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                  Rejection Reason *\n                </label>\n                <textarea\n                  value={rejectionReason}\n                  onChange={(e) => setRejectionReason(e.target.value)}\n                  className=\"w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white placeholder-slate-400 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent\"\n                  rows={3}\n                  placeholder=\"Please provide a reason for rejection...\"\n                  required\n                />\n              </div>\n            )}\n\n            <div className=\"flex gap-3\">\n              <Button\n                variant=\"outline\"\n                onClick={() => {\n                  setSelectedUser(null);\n                  setReviewAction(null);\n                  setRejectionReason('');\n                }}\n                disabled={processing}\n                className=\"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white\"\n              >\n                Cancel\n              </Button>\n              <Button\n                onClick={() => handleReview(\n                  selectedUser.userId,\n                  reviewAction,\n                  reviewAction === 'reject' ? rejectionReason : undefined\n                )}\n                disabled={processing || (reviewAction === 'reject' && !rejectionReason.trim())}\n                loading={processing}\n                className={reviewAction === 'approve'\n                  ? 'bg-green-600 hover:bg-green-700 text-white'\n                  : 'bg-red-600 hover:bg-red-700 text-white'\n                }\n              >\n                {reviewAction === 'approve' ? 'Approve' : 'Reject'}\n              </Button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AAnBA;;;;;;AAmDO,MAAM,YAAsB;IACjC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IAChD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IAChE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACjE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAC7E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA+B;IAC9E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,0BAA0B;IAC1B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA+C;IAE9F,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAO;QAAY;KAAa;IAEpC,MAAM,eAAe;QACnB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,SAAS,KAAK,IAAI;gBACpB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,WAAW;QAEf,sBAAsB;QACtB,IAAI,YAAY;YACd,WAAW,SAAS,MAAM,CAAC,CAAA,OACzB,KAAK,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACjE,KAAK,IAAI,CAAC,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAChE,KAAK,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC7D,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAEtE;QAEA,sBAAsB;QACtB,IAAI,iBAAiB,OAAO;YAC1B,WAAW,SAAS,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;QACrD;QAEA,iBAAiB;IACnB;IAEA,MAAM,eAAe,OAAO,QAAgB,QAA8B;QACxE,IAAI;YACF,cAAc;YACd,MAAM,WAAW,MAAM,MAAM,yBAAyB;gBACpD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,aAAa;gBACb,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA,QAAQ,OAAO,WAAW;oBAC1B,iBAAiB;gBACnB;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,gBAAgB,mBAAmB;gBACnC,gBAAgB;gBAChB,oBAAoB;gBACpB,gBAAgB;gBAChB,mBAAmB;YACrB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,SAAS;YACb,SAAS;YACT,UAAU;YACV,UAAU;QACZ;QAEA,qBACE,8OAAC;YAAK,WAAW,CAAC,wEAAwE,EAAE,MAAM,CAAC,OAA8B,EAAE;sBAChI;;;;;;IAGP;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAIvB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAgC;;;;;;0CAC9C,8OAAC;gCAAE,WAAU;0CAAsB;;;;;;;;;;;;kCAErC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,UAAU;gCACV,WAAU;;kDAEV,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAW,CAAC,aAAa,EAAE,UAAU,iBAAiB,IAAI;;;;;;oCAAI;;;;;;;0CAG3E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;oCACxB,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;oCAAC;;;;;;;;;;;;;;;;;;;0BAMhE,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC,iIAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;;;;;;;;;;;;;;;;8CAMhB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wCAC/C,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,8OAAC;gDAAO,OAAM;0DAAU;;;;;;0DACxB,8OAAC;gDAAO,OAAM;0DAAW;;;;;;0DACzB,8OAAC;gDAAO,OAAM;0DAAW;;;;;;;;;;;;;;;;;;;;;;;sCAM/B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;wCAAK;wCAAS,cAAc,MAAM;wCAAC;wCAAK,MAAM,MAAM;wCAAC;;;;;;;8CACtD,8OAAC;8CAAK;;;;;;8CACN,8OAAC;;wCAAM,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;wCAAC;;;;;;;8CAChE,8OAAC;8CAAK;;;;;;8CACN,8OAAC;;wCAAM,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,YAAY,MAAM;wCAAC;;;;;;;8CACjE,8OAAC;8CAAK;;;;;;8CACN,8OAAC;;wCAAM,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,YAAY,MAAM;wCAAC;;;;;;;;;;;;;;;;;;;;;;;;0BAMvE,8OAAC;gBAAI,WAAU;0BACZ,cAAc,MAAM,KAAK,kBACxB,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCAAG,WAAU;0CACX,MAAM,MAAM,KAAK,IAAI,uBAAuB;;;;;;0CAE/C,8OAAC;gCAAE,WAAU;0CACV,MAAM,MAAM,KAAK,IACd,+CACA;;;;;;;;;;;;;;;;2BAMV,cAAc,GAAG,CAAC,CAAC,wBACjB,8OAAC,gIAAA,CAAA,OAAI;wBAAsB,WAAU;kCACnC,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;gEAAK,WAAU;;oEACb,QAAQ,IAAI,CAAC,SAAS;oEAAC;oEAAE,QAAQ,IAAI,CAAC,QAAQ;;;;;;;;;;;;;oDAGlD,eAAe,QAAQ,MAAM;;;;;;;0DAGhC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAK,WAAU;0EAA6B;;;;;;4DAAa;4DAAE,QAAQ,IAAI,CAAC,KAAK;;;;;;;kEAEhF,8OAAC;;0EACC,8OAAC;gEAAK,WAAU;0EAA6B;;;;;;4DAAe;4DAAE,QAAQ,IAAI,CAAC,UAAU;;;;;;;kEAEvF,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;gEAAK,WAAU;0EAA6B;;;;;;4DAAiB;4DAAE,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,WAAW;;;;;;;;;;;;;0DAIlG,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,8MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;wDAAK,WAAU;kEAA6B;;;;;;oDAAiB;oDAAE,QAAQ,SAAS,CAAC,MAAM;oDAAC;;;;;;;0DAI3F,8OAAC;gDAAI,WAAU;0DACZ,QAAQ,SAAS,CAAC,GAAG,CAAC,CAAC,oBACtB,8OAAC;wDAAiB,WAAU;;0EAC1B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFACb,IAAI,YAAY,KAAK,WAAW,WAChC,GAAG,IAAI,MAAM,CAAC,GAAG,EAAE,IAAI,YAAY,EAAE;;;;;;oEAEvC,eAAe,IAAI,MAAM;;;;;;;0EAE5B,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS;wEACP,oBAAoB;wEACpB,sBAAsB;oEACxB;oEACA,WAAU;;sFAEV,8OAAC,0MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;4DAItC,IAAI,eAAe,kBAClB,8OAAC;gEAAE,WAAU;gEAAqC,OAAO,IAAI,eAAe;0EACzE,IAAI,eAAe;;;;;;;uDAxBhB,IAAI,EAAE;;;;;;;;;;;;;;;;kDAgCtB,8OAAC;wCAAI,WAAU;kDACZ,QAAQ,MAAM,KAAK,2BAClB;;8DACE,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;wDACP,gBAAgB;wDAChB,gBAAgB;oDAClB;oDACA,WAAU;;sEAEV,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAIpC,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;wDACP,gBAAgB;wDAChB,gBAAgB;oDAClB;oDACA,WAAU;;sEAEV,8OAAC,4LAAA,CAAA,IAAC;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;uBA5FjC,QAAQ,MAAM;;;;;;;;;;YA0G9B,sBAAsB,kCACrB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;wCAAmC;wCAC5B,iBAAiB,YAAY,KAAK,WAAW,WAChE,GAAG,iBAAiB,MAAM,CAAC,CAAC,EAAE,iBAAiB,YAAY,EAAE;;;;;;;8CAE/D,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;wCACP,sBAAsB;wCACtB,oBAAoB;oCACtB;oCACA,WAAU;8CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAGjB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,KAAK,iBAAiB,WAAW;gCACjC,KAAI;gCACJ,WAAU;;;;;;;;;;;;;;;;;;;;;;YAQnB,gBAAgB,8BACf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX,iBAAiB,YAAY,2BAA2B;;;;;;sCAG3D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;wCAAsB;sDAC3B,8OAAC;4CAAK,WAAU;;gDAA0B,aAAa,IAAI,CAAC,SAAS;gDAAC;gDAAE,aAAa,IAAI,CAAC,QAAQ;;;;;;;;;;;;;8CAE1G,8OAAC;oCAAE,WAAU;;wCAAsB;sDAC1B,8OAAC;4CAAK,WAAU;sDAA0B,aAAa,IAAI,CAAC,KAAK;;;;;;;;;;;;8CAE1E,8OAAC;oCAAE,WAAU;;wCAAiB;sDACjB,8OAAC;4CAAK,WAAU;;gDAA0B,aAAa,SAAS,CAAC,MAAM;gDAAC;;;;;;;;;;;;;;;;;;;wBAItF,iBAAiB,0BAChB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;8CAAgD;;;;;;8CAGjE,8OAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;oCAClD,WAAU;oCACV,MAAM;oCACN,aAAY;oCACZ,QAAQ;;;;;;;;;;;;sCAKd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;wCACP,gBAAgB;wCAChB,gBAAgB;wCAChB,mBAAmB;oCACrB;oCACA,UAAU;oCACV,WAAU;8CACX;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS,IAAM,aACb,aAAa,MAAM,EACnB,cACA,iBAAiB,WAAW,kBAAkB;oCAEhD,UAAU,cAAe,iBAAiB,YAAY,CAAC,gBAAgB,IAAI;oCAC3E,SAAS;oCACT,WAAW,iBAAiB,YACxB,+CACA;8CAGH,iBAAiB,YAAY,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1D", "debugId": null}}, {"offset": {"line": 2922, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/admin/DepositManagement.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardContent } from '@/components/ui';\nimport {\n  DollarSign,\n  Clock,\n  CheckCircle,\n  XCircle,\n  AlertTriangle,\n  Eye,\n  Filter,\n  Download,\n  RefreshCw,\n  Info\n} from 'lucide-react';\nimport { formatCurrency, formatDateTime } from '@/lib/utils';\nimport { DepositTransaction, DepositStatus } from '@/types';\n\ninterface DepositStats {\n  totalDeposits: number;\n  totalAmount: number;\n  pendingDeposits: number;\n}\n\ninterface DepositManagementProps {}\n\nexport const DepositManagement: React.FC<DepositManagementProps> = () => {\n  const [deposits, setDeposits] = useState<DepositTransaction[]>([]);\n  const [stats, setStats] = useState<DepositStats>({\n    totalDeposits: 0,\n    totalAmount: 0,\n    pendingDeposits: 0,\n  });\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [selectedStatus, setSelectedStatus] = useState<DepositStatus | 'ALL'>('ALL');\n  const [selectedDeposit, setSelectedDeposit] = useState<DepositTransaction | null>(null);\n  const [actionLoading, setActionLoading] = useState<string | null>(null);\n\n  const fetchDeposits = async () => {\n    try {\n      setLoading(true);\n      const params = new URLSearchParams();\n      if (selectedStatus !== 'ALL') {\n        params.append('status', selectedStatus);\n      }\n      params.append('limit', '50');\n\n      const response = await fetch(`/api/admin/deposits?${params}`, {\n        credentials: 'include',\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to fetch deposits');\n      }\n\n      const data = await response.json();\n      if (data.success) {\n        setDeposits(data.data.deposits);\n        setStats(data.data.stats);\n      } else {\n        throw new Error(data.error || 'Failed to fetch deposits');\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An error occurred');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDepositAction = async (transactionId: string, action: string, reason?: string) => {\n    try {\n      setActionLoading(transactionId);\n      \n      const response = await fetch('/api/admin/deposits', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include',\n        body: JSON.stringify({\n          action,\n          transactionId,\n          reason,\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to process deposit action');\n      }\n\n      const data = await response.json();\n      if (data.success) {\n        // Refresh deposits list\n        await fetchDeposits();\n        setSelectedDeposit(null);\n      } else {\n        throw new Error(data.error || 'Failed to process deposit action');\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An error occurred');\n    } finally {\n      setActionLoading(null);\n    }\n  };\n\n  useEffect(() => {\n    fetchDeposits();\n  }, [selectedStatus]);\n\n  const getStatusIcon = (status: DepositStatus) => {\n    switch (status) {\n      case 'COMPLETED':\n        return <CheckCircle className=\"w-4 h-4 text-green-400\" />;\n      case 'PENDING':\n        return <Clock className=\"w-4 h-4 text-yellow-400\" />;\n      case 'FAILED':\n      case 'REJECTED':\n        return <XCircle className=\"w-4 h-4 text-red-400\" />;\n      case 'VERIFYING':\n        return <AlertTriangle className=\"w-4 h-4 text-blue-400\" />;\n      default:\n        return <Clock className=\"w-4 h-4 text-gray-400\" />;\n    }\n  };\n\n  const getStatusColor = (status: DepositStatus) => {\n    switch (status) {\n      case 'COMPLETED':\n        return 'text-green-400 bg-green-400/10';\n      case 'PENDING':\n        return 'text-yellow-400 bg-yellow-400/10';\n      case 'FAILED':\n      case 'REJECTED':\n        return 'text-red-400 bg-red-400/10';\n      case 'VERIFYING':\n        return 'text-blue-400 bg-blue-400/10';\n      default:\n        return 'text-gray-400 bg-gray-400/10';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <RefreshCw className=\"w-8 h-8 animate-spin text-blue-400\" />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <h1 className=\"text-2xl font-bold text-white\">Deposit Management</h1>\n        <div className=\"flex items-center space-x-4\">\n          <button\n            onClick={fetchDeposits}\n            className=\"flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors\"\n          >\n            <RefreshCw className=\"w-4 h-4\" />\n            <span>Refresh</span>\n          </button>\n        </div>\n      </div>\n\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        <Card className=\"bg-gray-800 border-gray-700\">\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-gray-400 text-sm\">Total Deposits</p>\n                <p className=\"text-2xl font-bold text-white\">{stats.totalDeposits}</p>\n              </div>\n              <DollarSign className=\"w-8 h-8 text-green-400\" />\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card className=\"bg-gray-800 border-gray-700\">\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-gray-400 text-sm\">Total Amount</p>\n                <p className=\"text-2xl font-bold text-white\">{formatCurrency(stats.totalAmount)}</p>\n              </div>\n              <DollarSign className=\"w-8 h-8 text-blue-400\" />\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card className=\"bg-gray-800 border-gray-700\">\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-gray-400 text-sm\">Pending Deposits</p>\n                <p className=\"text-2xl font-bold text-white\">{stats.pendingDeposits}</p>\n              </div>\n              <Clock className=\"w-8 h-8 text-yellow-400\" />\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Filters */}\n      <Card className=\"bg-gray-800 border-gray-700\">\n        <CardContent className=\"p-6\">\n          <div className=\"flex items-center space-x-4\">\n            <Filter className=\"w-5 h-5 text-gray-400\" />\n            <select\n              value={selectedStatus}\n              onChange={(e) => setSelectedStatus(e.target.value as DepositStatus | 'ALL')}\n              className=\"bg-gray-700 border border-gray-600 text-white rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            >\n              <option value=\"ALL\">All Status</option>\n              <option value=\"PENDING\">Pending</option>\n              <option value=\"VERIFYING\">Verifying</option>\n              <option value=\"CONFIRMED\">Confirmed</option>\n              <option value=\"COMPLETED\">Completed</option>\n              <option value=\"FAILED\">Failed</option>\n              <option value=\"REJECTED\">Rejected</option>\n            </select>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Deposits Table */}\n      <Card className=\"bg-gray-800 border-gray-700\">\n        <CardHeader>\n          <CardTitle className=\"text-white\">Recent Deposits</CardTitle>\n        </CardHeader>\n        <CardContent>\n          {error && (\n            <div className=\"mb-4 p-4 bg-red-900/20 border border-red-500 rounded-lg\">\n              <p className=\"text-red-400\">{error}</p>\n            </div>\n          )}\n\n          <div className=\"overflow-x-auto\">\n            <table className=\"w-full\">\n              <thead>\n                <tr className=\"border-b border-gray-700\">\n                  <th className=\"text-left py-3 px-4 text-gray-400 font-medium\">User</th>\n                  <th className=\"text-left py-3 px-4 text-gray-400 font-medium\">Amount</th>\n                  <th className=\"text-left py-3 px-4 text-gray-400 font-medium\">Status</th>\n                  <th className=\"text-left py-3 px-4 text-gray-400 font-medium\">Transaction ID</th>\n                  <th className=\"text-left py-3 px-4 text-gray-400 font-medium\">Date</th>\n                  <th className=\"text-left py-3 px-4 text-gray-400 font-medium\">Actions</th>\n                </tr>\n              </thead>\n              <tbody>\n                {deposits.map((deposit) => (\n                  <tr key={deposit.id} className=\"border-b border-gray-700/50 hover:bg-gray-700/30\">\n                    <td className=\"py-3 px-4\">\n                      <div className=\"text-white\">\n                        {deposit.user ? `${deposit.user.firstName} ${deposit.user.lastName}` : 'Unknown'}\n                      </div>\n                      <div className=\"text-sm text-gray-400\">\n                        {deposit.user?.email || 'No email'}\n                      </div>\n                    </td>\n                    <td className=\"py-3 px-4\">\n                      <div className=\"text-white font-medium\">\n                        {formatCurrency(deposit.usdtAmount)} USDT\n                      </div>\n                      <div className=\"text-sm text-gray-400\">\n                        {deposit.confirmations} confirmations\n                      </div>\n                    </td>\n                    <td className=\"py-3 px-4\">\n                      <div className={`inline-flex items-center space-x-2 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(deposit.status)}`}>\n                        {getStatusIcon(deposit.status)}\n                        <span>{deposit.status}</span>\n                      </div>\n                    </td>\n                    <td className=\"py-3 px-4\">\n                      <div className=\"text-white font-mono text-sm\">\n                        {deposit.transactionId.slice(0, 8)}...{deposit.transactionId.slice(-8)}\n                      </div>\n                    </td>\n                    <td className=\"py-3 px-4 text-gray-300\">\n                      {formatDateTime(deposit.createdAt)}\n                    </td>\n                    <td className=\"py-3 px-4\">\n                      <button\n                        onClick={() => setSelectedDeposit(deposit)}\n                        className=\"flex items-center space-x-1 px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm transition-colors\"\n                      >\n                        <Eye className=\"w-4 h-4\" />\n                        <span>View</span>\n                      </button>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n\n            {deposits.length === 0 && (\n              <div className=\"text-center py-8 text-gray-400\">\n                No deposits found\n              </div>\n            )}\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Deposit Detail Modal */}\n      {selectedDeposit && (\n        <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50\">\n          <div className=\"bg-gray-800 border border-gray-700 rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto\">\n            <div className=\"flex items-center justify-between mb-6\">\n              <h2 className=\"text-xl font-bold text-white\">Deposit Details</h2>\n              <button\n                onClick={() => setSelectedDeposit(null)}\n                className=\"text-gray-400 hover:text-white\"\n              >\n                <XCircle className=\"w-6 h-6\" />\n              </button>\n            </div>\n\n            <div className=\"space-y-4\">\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"text-gray-400 text-sm\">User</label>\n                  <p className=\"text-white\">\n                    {selectedDeposit.user ? `${selectedDeposit.user.firstName} ${selectedDeposit.user.lastName}` : 'Unknown'}\n                  </p>\n                  <p className=\"text-gray-400 text-sm\">{selectedDeposit.user?.email}</p>\n                </div>\n                <div>\n                  <label className=\"text-gray-400 text-sm\">Amount</label>\n                  <p className=\"text-white font-medium\">{formatCurrency(selectedDeposit.usdtAmount)} USDT</p>\n                </div>\n              </div>\n\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"text-gray-400 text-sm\">Status</label>\n                  <div className={`inline-flex items-center space-x-2 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(selectedDeposit.status)}`}>\n                    {getStatusIcon(selectedDeposit.status)}\n                    <span>{selectedDeposit.status}</span>\n                  </div>\n                </div>\n                <div>\n                  <label className=\"text-gray-400 text-sm\">Confirmations</label>\n                  <p className=\"text-white\">{selectedDeposit.confirmations}</p>\n                </div>\n              </div>\n\n              <div>\n                <label className=\"text-gray-400 text-sm\">Transaction ID</label>\n                <p className=\"text-white font-mono text-sm break-all\">{selectedDeposit.transactionId}</p>\n              </div>\n\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"text-gray-400 text-sm\">Sender Address</label>\n                  <p className=\"text-white font-mono text-sm break-all\">\n                    {selectedDeposit.senderAddress || 'N/A'}\n                  </p>\n                </div>\n                <div>\n                  <label className=\"text-gray-400 text-sm\">Deposit Address</label>\n                  <p className=\"text-white font-mono text-sm break-all\">{selectedDeposit.tronAddress}</p>\n                </div>\n              </div>\n\n              {selectedDeposit.failureReason && (\n                <div>\n                  <label className=\"text-gray-400 text-sm\">Failure Reason</label>\n                  <p className=\"text-red-400\">{selectedDeposit.failureReason}</p>\n                </div>\n              )}\n\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"text-gray-400 text-sm\">Created At</label>\n                  <p className=\"text-white\">{formatDateTime(selectedDeposit.createdAt)}</p>\n                </div>\n                <div>\n                  <label className=\"text-gray-400 text-sm\">Processed At</label>\n                  <p className=\"text-white\">\n                    {selectedDeposit.processedAt ? formatDateTime(selectedDeposit.processedAt) : 'Not processed'}\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            {/* Automated Processing Notice */}\n            <div className=\"mt-6 pt-6 border-t border-gray-700\">\n              <div className=\"bg-blue-900/50 border border-blue-700 rounded-lg p-4\">\n                <div className=\"flex items-center space-x-2\">\n                  <Info className=\"w-5 h-5 text-blue-400\" />\n                  <div>\n                    <h4 className=\"text-blue-300 font-medium\">Automated Processing</h4>\n                    <p className=\"text-blue-200 text-sm mt-1\">\n                      Deposits are now processed automatically. The system verifies transactions and credits wallets once confirmations are met.\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAhBA;;;;;;AA2BO,MAAM,oBAAsD;IACjE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB,EAAE;IACjE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;QAC/C,eAAe;QACf,aAAa;QACb,iBAAiB;IACnB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAC5E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B;IAClF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElE,MAAM,gBAAgB;QACpB,IAAI;YACF,WAAW;YACX,MAAM,SAAS,IAAI;YACnB,IAAI,mBAAmB,OAAO;gBAC5B,OAAO,MAAM,CAAC,UAAU;YAC1B;YACA,OAAO,MAAM,CAAC,SAAS;YAEvB,MAAM,WAAW,MAAM,MAAM,CAAC,oBAAoB,EAAE,QAAQ,EAAE;gBAC5D,aAAa;YACf;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,OAAO,EAAE;gBAChB,YAAY,KAAK,IAAI,CAAC,QAAQ;gBAC9B,SAAS,KAAK,IAAI,CAAC,KAAK;YAC1B,OAAO;gBACL,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,sBAAsB,OAAO,eAAuB,QAAgB;QACxE,IAAI;YACF,iBAAiB;YAEjB,MAAM,WAAW,MAAM,MAAM,uBAAuB;gBAClD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,aAAa;gBACb,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA;oBACA;gBACF;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,OAAO,EAAE;gBAChB,wBAAwB;gBACxB,MAAM;gBACN,mBAAmB;YACrB,OAAO;gBACL,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAe;IAEnB,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,4MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC;gBACE,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QAC5B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;IAG3B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAgC;;;;;;kCAC9C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,8OAAC;gDAAE,WAAU;0DAAiC,MAAM,aAAa;;;;;;;;;;;;kDAEnE,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK5B,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,8OAAC;gDAAE,WAAU;0DAAiC,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,WAAW;;;;;;;;;;;;kDAEhF,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK5B,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,8OAAC;gDAAE,WAAU;0DAAiC,MAAM,eAAe;;;;;;;;;;;;kDAErE,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOzB,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;gCACjD,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAM;;;;;;kDACpB,8OAAC;wCAAO,OAAM;kDAAU;;;;;;kDACxB,8OAAC;wCAAO,OAAM;kDAAY;;;;;;kDAC1B,8OAAC;wCAAO,OAAM;kDAAY;;;;;;kDAC1B,8OAAC;wCAAO,OAAM;kDAAY;;;;;;kDAC1B,8OAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,8OAAC;wCAAO,OAAM;kDAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOjC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAa;;;;;;;;;;;kCAEpC,8OAAC,gIAAA,CAAA,cAAW;;4BACT,uBACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;0CAIjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;0DACC,cAAA,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAG,WAAU;sEAAgD;;;;;;sEAC9D,8OAAC;4DAAG,WAAU;sEAAgD;;;;;;sEAC9D,8OAAC;4DAAG,WAAU;sEAAgD;;;;;;sEAC9D,8OAAC;4DAAG,WAAU;sEAAgD;;;;;;sEAC9D,8OAAC;4DAAG,WAAU;sEAAgD;;;;;;sEAC9D,8OAAC;4DAAG,WAAU;sEAAgD;;;;;;;;;;;;;;;;;0DAGlE,8OAAC;0DACE,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;wDAAoB,WAAU;;0EAC7B,8OAAC;gEAAG,WAAU;;kFACZ,8OAAC;wEAAI,WAAU;kFACZ,QAAQ,IAAI,GAAG,GAAG,QAAQ,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,QAAQ,IAAI,CAAC,QAAQ,EAAE,GAAG;;;;;;kFAEzE,8OAAC;wEAAI,WAAU;kFACZ,QAAQ,IAAI,EAAE,SAAS;;;;;;;;;;;;0EAG5B,8OAAC;gEAAG,WAAU;;kFACZ,8OAAC;wEAAI,WAAU;;4EACZ,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,UAAU;4EAAE;;;;;;;kFAEtC,8OAAC;wEAAI,WAAU;;4EACZ,QAAQ,aAAa;4EAAC;;;;;;;;;;;;;0EAG3B,8OAAC;gEAAG,WAAU;0EACZ,cAAA,8OAAC;oEAAI,WAAW,CAAC,8EAA8E,EAAE,eAAe,QAAQ,MAAM,GAAG;;wEAC9H,cAAc,QAAQ,MAAM;sFAC7B,8OAAC;sFAAM,QAAQ,MAAM;;;;;;;;;;;;;;;;;0EAGzB,8OAAC;gEAAG,WAAU;0EACZ,cAAA,8OAAC;oEAAI,WAAU;;wEACZ,QAAQ,aAAa,CAAC,KAAK,CAAC,GAAG;wEAAG;wEAAI,QAAQ,aAAa,CAAC,KAAK,CAAC,CAAC;;;;;;;;;;;;0EAGxE,8OAAC;gEAAG,WAAU;0EACX,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,SAAS;;;;;;0EAEnC,8OAAC;gEAAG,WAAU;0EACZ,cAAA,8OAAC;oEACC,SAAS,IAAM,mBAAmB;oEAClC,WAAU;;sFAEV,8OAAC,gMAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;sFACf,8OAAC;sFAAK;;;;;;;;;;;;;;;;;;uDArCH,QAAQ,EAAE;;;;;;;;;;;;;;;;oCA6CxB,SAAS,MAAM,KAAK,mBACnB,8OAAC;wCAAI,WAAU;kDAAiC;;;;;;;;;;;;;;;;;;;;;;;;YASvD,iCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA+B;;;;;;8CAC7C,8OAAC;oCACC,SAAS,IAAM,mBAAmB;oCAClC,WAAU;8CAEV,cAAA,8OAAC,4MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAIvB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAwB;;;;;;8DACzC,8OAAC;oDAAE,WAAU;8DACV,gBAAgB,IAAI,GAAG,GAAG,gBAAgB,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,gBAAgB,IAAI,CAAC,QAAQ,EAAE,GAAG;;;;;;8DAEjG,8OAAC;oDAAE,WAAU;8DAAyB,gBAAgB,IAAI,EAAE;;;;;;;;;;;;sDAE9D,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAwB;;;;;;8DACzC,8OAAC;oDAAE,WAAU;;wDAA0B,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,gBAAgB,UAAU;wDAAE;;;;;;;;;;;;;;;;;;;8CAItF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAwB;;;;;;8DACzC,8OAAC;oDAAI,WAAW,CAAC,8EAA8E,EAAE,eAAe,gBAAgB,MAAM,GAAG;;wDACtI,cAAc,gBAAgB,MAAM;sEACrC,8OAAC;sEAAM,gBAAgB,MAAM;;;;;;;;;;;;;;;;;;sDAGjC,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAwB;;;;;;8DACzC,8OAAC;oDAAE,WAAU;8DAAc,gBAAgB,aAAa;;;;;;;;;;;;;;;;;;8CAI5D,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAwB;;;;;;sDACzC,8OAAC;4CAAE,WAAU;sDAA0C,gBAAgB,aAAa;;;;;;;;;;;;8CAGtF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAwB;;;;;;8DACzC,8OAAC;oDAAE,WAAU;8DACV,gBAAgB,aAAa,IAAI;;;;;;;;;;;;sDAGtC,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAwB;;;;;;8DACzC,8OAAC;oDAAE,WAAU;8DAA0C,gBAAgB,WAAW;;;;;;;;;;;;;;;;;;gCAIrF,gBAAgB,aAAa,kBAC5B,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAwB;;;;;;sDACzC,8OAAC;4CAAE,WAAU;sDAAgB,gBAAgB,aAAa;;;;;;;;;;;;8CAI9D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAwB;;;;;;8DACzC,8OAAC;oDAAE,WAAU;8DAAc,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,gBAAgB,SAAS;;;;;;;;;;;;sDAErE,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAwB;;;;;;8DACzC,8OAAC;oDAAE,WAAU;8DACV,gBAAgB,WAAW,GAAG,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,gBAAgB,WAAW,IAAI;;;;;;;;;;;;;;;;;;;;;;;;sCAOrF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA4B;;;;;;8DAC1C,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY9D", "debugId": null}}, {"offset": {"line": 4124, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/admin/WithdrawalManagement.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { <PERSON>, Card<PERSON>eader, Card<PERSON>itle, CardContent, Button, Input } from '@/components/ui';\nimport { \n  CreditCard, \n  Search, \n  Filter, \n  Check, \n  X, \n  Clock,\n  DollarSign,\n  User,\n  Calendar,\n  AlertTriangle,\n  CheckCircle,\n  XCircle\n} from 'lucide-react';\nimport { formatCurrency, formatDateTime } from '@/lib/utils';\n\ninterface WithdrawalRequest {\n  id: string;\n  userId: string;\n  user: {\n    firstName: string;\n    lastName: string;\n    email: string;\n    referralId: string;\n  };\n  amount: number;\n  walletAddress: string;\n  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'COMPLETED';\n  requestedAt: string;\n  processedAt?: string;\n  rejectionReason?: string;\n  transactionHash?: string;\n}\n\nexport const WithdrawalManagement: React.FC = () => {\n  const [withdrawals, setWithdrawals] = useState<WithdrawalRequest[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterStatus, setFilterStatus] = useState<'all' | 'pending' | 'approved' | 'rejected' | 'completed'>('all');\n  const [selectedWithdrawal, setSelectedWithdrawal] = useState<WithdrawalRequest | null>(null);\n  const [reviewAction, setReviewAction] = useState<'approve' | 'reject' | 'complete' | null>(null);\n  const [rejectionReason, setRejectionReason] = useState('');\n  const [transactionHash, setTransactionHash] = useState('');\n  const [processing, setProcessing] = useState(false);\n\n  useEffect(() => {\n    fetchWithdrawals();\n  }, [searchTerm, filterStatus]);\n\n  const fetchWithdrawals = async () => {\n    try {\n      setLoading(true);\n      const params = new URLSearchParams({\n        search: searchTerm,\n        status: filterStatus,\n      });\n\n      const response = await fetch(`/api/admin/withdrawals?${params}`, {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setWithdrawals(data.data);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch withdrawals:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleWithdrawalAction = async (\n    withdrawalId: string, \n    action: 'approve' | 'reject' | 'complete', \n    data?: { rejectionReason?: string; transactionHash?: string }\n  ) => {\n    try {\n      setProcessing(true);\n      const response = await fetch('/api/admin/withdrawals/action', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include',\n        body: JSON.stringify({\n          withdrawalId,\n          action: action.toUpperCase(),\n          ...data,\n        }),\n      });\n\n      if (response.ok) {\n        fetchWithdrawals(); // Refresh the list\n        setSelectedWithdrawal(null);\n        setReviewAction(null);\n        setRejectionReason('');\n        setTransactionHash('');\n      }\n    } catch (error) {\n      console.error('Failed to process withdrawal action:', error);\n    } finally {\n      setProcessing(false);\n    }\n  };\n\n  const getStatusBadge = (status: string) => {\n    const configs = {\n      PENDING: { color: 'bg-yellow-900 text-yellow-300 border border-yellow-700', icon: Clock },\n      APPROVED: { color: 'bg-blue-900 text-blue-300 border border-blue-700', icon: CheckCircle },\n      REJECTED: { color: 'bg-red-900 text-red-300 border border-red-700', icon: XCircle },\n      COMPLETED: { color: 'bg-green-900 text-green-300 border border-green-700', icon: CheckCircle },\n    };\n\n    const config = configs[status as keyof typeof configs];\n    const Icon = config.icon;\n\n    return (\n      <span className={`inline-flex items-center gap-1 px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>\n        <Icon className=\"h-3 w-3\" />\n        {status}\n      </span>\n    );\n  };\n\n  const getTotalPendingAmount = () => {\n    return withdrawals\n      .filter(w => w.status === 'PENDING')\n      .reduce((sum, w) => sum + w.amount, 0);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-8 bg-slate-700 rounded w-1/4 mb-4\"></div>\n          <div className=\"h-64 bg-slate-700 rounded\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-white\">Withdrawal Management</h1>\n          <p className=\"text-slate-400 mt-1\">Review and process user withdrawal requests</p>\n        </div>\n        <div className=\"text-right\">\n          <div className=\"text-sm text-slate-400\">Pending Amount</div>\n          <div className=\"text-2xl font-bold text-yellow-400\">\n            {formatCurrency(getTotalPendingAmount())}\n          </div>\n        </div>\n      </div>\n\n      {/* Filters */}\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardContent className=\"p-6\">\n          <div className=\"flex flex-col sm:flex-row gap-4\">\n            <div className=\"flex-1\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4\" />\n                <Input\n                  placeholder=\"Search by user email or wallet address...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-red-500\"\n                />\n              </div>\n            </div>\n            <div className=\"sm:w-48\">\n              <select\n                value={filterStatus}\n                onChange={(e) => setFilterStatus(e.target.value as any)}\n                className=\"w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent\"\n              >\n                <option value=\"all\">All Withdrawals</option>\n                <option value=\"pending\">Pending</option>\n                <option value=\"approved\">Approved</option>\n                <option value=\"completed\">Completed</option>\n                <option value=\"rejected\">Rejected</option>\n              </select>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Withdrawals List */}\n      <div className=\"grid gap-4\">\n        {withdrawals.length === 0 ? (\n          <Card className=\"bg-slate-800 border-slate-700\">\n            <CardContent className=\"p-12 text-center\">\n              <CreditCard className=\"h-12 w-12 text-slate-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium text-white mb-2\">No Withdrawal Requests</h3>\n              <p className=\"text-slate-400\">No withdrawal requests match your current filters.</p>\n            </CardContent>\n          </Card>\n        ) : (\n          withdrawals.map((withdrawal) => (\n            <Card key={withdrawal.id} className=\"bg-slate-800 border-slate-700 hover:bg-slate-750 transition-colors\">\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center gap-3 mb-3\">\n                      <div className=\"flex items-center gap-2\">\n                        <User className=\"h-5 w-5 text-slate-400\" />\n                        <span className=\"font-medium text-white\">\n                          {withdrawal.user.firstName} {withdrawal.user.lastName}\n                        </span>\n                      </div>\n                      {getStatusBadge(withdrawal.status)}\n                    </div>\n\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-slate-400 mb-4\">\n                      <div>\n                        <span className=\"font-medium text-slate-300\">Email:</span> {withdrawal.user.email}\n                      </div>\n                      <div>\n                        <span className=\"font-medium text-slate-300\">User ID:</span> {withdrawal.user.referralId}\n                      </div>\n                      <div className=\"flex items-center gap-1\">\n                        <DollarSign className=\"h-4 w-4\" />\n                        <span className=\"font-medium text-slate-300\">Amount:</span> {formatCurrency(withdrawal.amount)}\n                      </div>\n                      <div className=\"flex items-center gap-1\">\n                        <Calendar className=\"h-4 w-4\" />\n                        <span className=\"font-medium text-slate-300\">Requested:</span> {formatDateTime(withdrawal.requestedAt)}\n                      </div>\n                    </div>\n\n                    <div className=\"text-sm text-slate-400 mb-4\">\n                      <span className=\"font-medium text-slate-300\">Wallet Address:</span>\n                      <div className=\"font-mono text-xs bg-slate-700 border border-slate-600 p-2 rounded mt-1 break-all text-slate-300\">\n                        {withdrawal.walletAddress}\n                      </div>\n                    </div>\n\n                    {withdrawal.transactionHash && (\n                      <div className=\"text-sm text-slate-400 mb-4\">\n                        <span className=\"font-medium text-slate-300\">Transaction Hash:</span>\n                        <div className=\"font-mono text-xs bg-green-900/20 border border-green-700 p-2 rounded mt-1 break-all text-green-300\">\n                          {withdrawal.transactionHash}\n                        </div>\n                      </div>\n                    )}\n\n                    {withdrawal.rejectionReason && (\n                      <div className=\"bg-red-900/20 border border-red-700 rounded-lg p-3 mb-4\">\n                        <div className=\"flex items-center gap-2 text-red-300 text-sm font-medium mb-1\">\n                          <X className=\"h-4 w-4\" />\n                          Rejection Reason\n                        </div>\n                        <p className=\"text-red-400 text-sm\">{withdrawal.rejectionReason}</p>\n                      </div>\n                    )}\n                  </div>\n\n                  <div className=\"flex items-center gap-2 ml-4\">\n                    {withdrawal.status === 'PENDING' && (\n                      <>\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={() => {\n                            setSelectedWithdrawal(withdrawal);\n                            setReviewAction('approve');\n                          }}\n                          className=\"border-green-600 text-green-400 hover:text-green-300 hover:bg-green-900/20\"\n                        >\n                          <Check className=\"h-4 w-4 mr-1\" />\n                          Approve\n                        </Button>\n\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={() => {\n                            setSelectedWithdrawal(withdrawal);\n                            setReviewAction('reject');\n                          }}\n                          className=\"border-red-600 text-red-400 hover:text-red-300 hover:bg-red-900/20\"\n                        >\n                          <X className=\"h-4 w-4 mr-1\" />\n                          Reject\n                        </Button>\n                      </>\n                    )}\n\n                    {withdrawal.status === 'APPROVED' && (\n                      <Button\n                        variant=\"outline\"\n                        size=\"sm\"\n                        onClick={() => {\n                          setSelectedWithdrawal(withdrawal);\n                          setReviewAction('complete');\n                        }}\n                        className=\"border-blue-600 text-blue-400 hover:text-blue-300 hover:bg-blue-900/20\"\n                      >\n                        <CheckCircle className=\"h-4 w-4 mr-1\" />\n                        Mark Complete\n                      </Button>\n                    )}\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          ))\n        )}\n      </div>\n\n      {/* Action Modal */}\n      {selectedWithdrawal && reviewAction && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center p-4 z-50\">\n          <div className=\"bg-slate-800 border border-slate-700 rounded-xl max-w-md w-full p-6\">\n            <h3 className=\"text-lg font-semibold mb-4 text-white\">\n              {reviewAction === 'approve' && 'Approve Withdrawal'}\n              {reviewAction === 'reject' && 'Reject Withdrawal'}\n              {reviewAction === 'complete' && 'Complete Withdrawal'}\n            </h3>\n\n            <div className=\"mb-4\">\n              <p className=\"text-slate-400 mb-2\">\n                User: <span className=\"font-medium text-white\">{selectedWithdrawal.user.firstName} {selectedWithdrawal.user.lastName}</span>\n              </p>\n              <p className=\"text-slate-400\">\n                Amount: <span className=\"font-medium text-white\">{formatCurrency(selectedWithdrawal.amount)}</span>\n              </p>\n            </div>\n\n            {reviewAction === 'reject' && (\n              <div className=\"mb-4\">\n                <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                  Rejection Reason *\n                </label>\n                <textarea\n                  value={rejectionReason}\n                  onChange={(e) => setRejectionReason(e.target.value)}\n                  className=\"w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white placeholder-slate-400 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent\"\n                  rows={3}\n                  placeholder=\"Please provide a reason for rejection...\"\n                  required\n                />\n              </div>\n            )}\n\n            {reviewAction === 'complete' && (\n              <div className=\"mb-4\">\n                <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                  Transaction Hash *\n                </label>\n                <Input\n                  value={transactionHash}\n                  onChange={(e) => setTransactionHash(e.target.value)}\n                  placeholder=\"Enter blockchain transaction hash...\"\n                  className=\"bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500\"\n                  required\n                />\n              </div>\n            )}\n\n            <div className=\"flex gap-3\">\n              <Button\n                variant=\"outline\"\n                onClick={() => {\n                  setSelectedWithdrawal(null);\n                  setReviewAction(null);\n                  setRejectionReason('');\n                  setTransactionHash('');\n                }}\n                disabled={processing}\n                className=\"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white\"\n              >\n                Cancel\n              </Button>\n              <Button\n                onClick={() => {\n                  const data: any = {};\n                  if (reviewAction === 'reject') data.rejectionReason = rejectionReason;\n                  if (reviewAction === 'complete') data.transactionHash = transactionHash;\n\n                  handleWithdrawalAction(selectedWithdrawal.id, reviewAction, data);\n                }}\n                disabled={\n                  processing ||\n                  (reviewAction === 'reject' && !rejectionReason.trim()) ||\n                  (reviewAction === 'complete' && !transactionHash.trim())\n                }\n                loading={processing}\n                className={\n                  reviewAction === 'reject'\n                    ? 'bg-red-600 hover:bg-red-700 text-white'\n                    : reviewAction === 'approve'\n                    ? 'bg-green-600 hover:bg-green-700 text-white'\n                    : 'bg-blue-600 hover:bg-blue-700 text-white'\n                }\n              >\n                {reviewAction === 'approve' && 'Approve'}\n                {reviewAction === 'reject' && 'Reject'}\n                {reviewAction === 'complete' && 'Mark Complete'}\n              </Button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AAlBA;;;;;;AAsCO,MAAM,uBAAiC;IAC5C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6D;IAC5G,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B;IACvF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4C;IAC3F,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAY;KAAa;IAE7B,MAAM,mBAAmB;QACvB,IAAI;YACF,WAAW;YACX,MAAM,SAAS,IAAI,gBAAgB;gBACjC,QAAQ;gBACR,QAAQ;YACV;YAEA,MAAM,WAAW,MAAM,MAAM,CAAC,uBAAuB,EAAE,QAAQ,EAAE;gBAC/D,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,eAAe,KAAK,IAAI;gBAC1B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,yBAAyB,OAC7B,cACA,QACA;QAEA,IAAI;YACF,cAAc;YACd,MAAM,WAAW,MAAM,MAAM,iCAAiC;gBAC5D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,aAAa;gBACb,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA,QAAQ,OAAO,WAAW;oBAC1B,GAAG,IAAI;gBACT;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,oBAAoB,mBAAmB;gBACvC,sBAAsB;gBACtB,gBAAgB;gBAChB,mBAAmB;gBACnB,mBAAmB;YACrB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;QACxD,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,UAAU;YACd,SAAS;gBAAE,OAAO;gBAA0D,MAAM,oMAAA,CAAA,QAAK;YAAC;YACxF,UAAU;gBAAE,OAAO;gBAAoD,MAAM,2NAAA,CAAA,cAAW;YAAC;YACzF,UAAU;gBAAE,OAAO;gBAAiD,MAAM,4MAAA,CAAA,UAAO;YAAC;YAClF,WAAW;gBAAE,OAAO;gBAAuD,MAAM,2NAAA,CAAA,cAAW;YAAC;QAC/F;QAEA,MAAM,SAAS,OAAO,CAAC,OAA+B;QACtD,MAAM,OAAO,OAAO,IAAI;QAExB,qBACE,8OAAC;YAAK,WAAW,CAAC,8EAA8E,EAAE,OAAO,KAAK,EAAE;;8BAC9G,8OAAC;oBAAK,WAAU;;;;;;gBACf;;;;;;;IAGP;IAEA,MAAM,wBAAwB;QAC5B,OAAO,YACJ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WACzB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;IACxC;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAIvB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAgC;;;;;;0CAC9C,8OAAC;gCAAE,WAAU;0CAAsB;;;;;;;;;;;;kCAErC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAyB;;;;;;0CACxC,8OAAC;gCAAI,WAAU;0CACZ,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;;;;;;;0BAMtB,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC,iIAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;;;;;;;;;;;;;;;;0CAIhB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oCAC/C,WAAU;;sDAEV,8OAAC;4CAAO,OAAM;sDAAM;;;;;;sDACpB,8OAAC;4CAAO,OAAM;sDAAU;;;;;;sDACxB,8OAAC;4CAAO,OAAM;sDAAW;;;;;;sDACzB,8OAAC;4CAAO,OAAM;sDAAY;;;;;;sDAC1B,8OAAC;4CAAO,OAAM;sDAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQnC,8OAAC;gBAAI,WAAU;0BACZ,YAAY,MAAM,KAAK,kBACtB,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;0CACtB,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,8OAAC;gCAAE,WAAU;0CAAiB;;;;;;;;;;;;;;;;2BAIlC,YAAY,GAAG,CAAC,CAAC,2BACf,8OAAC,gIAAA,CAAA,OAAI;wBAAqB,WAAU;kCAClC,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;gEAAK,WAAU;;oEACb,WAAW,IAAI,CAAC,SAAS;oEAAC;oEAAE,WAAW,IAAI,CAAC,QAAQ;;;;;;;;;;;;;oDAGxD,eAAe,WAAW,MAAM;;;;;;;0DAGnC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAK,WAAU;0EAA6B;;;;;;4DAAa;4DAAE,WAAW,IAAI,CAAC,KAAK;;;;;;;kEAEnF,8OAAC;;0EACC,8OAAC;gEAAK,WAAU;0EAA6B;;;;;;4DAAe;4DAAE,WAAW,IAAI,CAAC,UAAU;;;;;;;kEAE1F,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;0EACtB,8OAAC;gEAAK,WAAU;0EAA6B;;;;;;4DAAc;4DAAE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,MAAM;;;;;;;kEAE/F,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;gEAAK,WAAU;0EAA6B;;;;;;4DAAiB;4DAAE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,WAAW;;;;;;;;;;;;;0DAIzG,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAA6B;;;;;;kEAC7C,8OAAC;wDAAI,WAAU;kEACZ,WAAW,aAAa;;;;;;;;;;;;4CAI5B,WAAW,eAAe,kBACzB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAA6B;;;;;;kEAC7C,8OAAC;wDAAI,WAAU;kEACZ,WAAW,eAAe;;;;;;;;;;;;4CAKhC,WAAW,eAAe,kBACzB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,4LAAA,CAAA,IAAC;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAG3B,8OAAC;wDAAE,WAAU;kEAAwB,WAAW,eAAe;;;;;;;;;;;;;;;;;;kDAKrE,8OAAC;wCAAI,WAAU;;4CACZ,WAAW,MAAM,KAAK,2BACrB;;kEACE,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;4DACP,sBAAsB;4DACtB,gBAAgB;wDAClB;wDACA,WAAU;;0EAEV,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAIpC,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;4DACP,sBAAsB;4DACtB,gBAAgB;wDAClB;wDACA,WAAU;;0EAEV,8OAAC,4LAAA,CAAA,IAAC;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;4CAMnC,WAAW,MAAM,KAAK,4BACrB,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;oDACP,sBAAsB;oDACtB,gBAAgB;gDAClB;gDACA,WAAU;;kEAEV,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;uBAnGzC,WAAW,EAAE;;;;;;;;;;YAgH7B,sBAAsB,8BACrB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCACX,iBAAiB,aAAa;gCAC9B,iBAAiB,YAAY;gCAC7B,iBAAiB,cAAc;;;;;;;sCAGlC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;wCAAsB;sDAC3B,8OAAC;4CAAK,WAAU;;gDAA0B,mBAAmB,IAAI,CAAC,SAAS;gDAAC;gDAAE,mBAAmB,IAAI,CAAC,QAAQ;;;;;;;;;;;;;8CAEtH,8OAAC;oCAAE,WAAU;;wCAAiB;sDACpB,8OAAC;4CAAK,WAAU;sDAA0B,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,mBAAmB,MAAM;;;;;;;;;;;;;;;;;;wBAI7F,iBAAiB,0BAChB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;8CAAgD;;;;;;8CAGjE,8OAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;oCAClD,WAAU;oCACV,MAAM;oCACN,aAAY;oCACZ,QAAQ;;;;;;;;;;;;wBAKb,iBAAiB,4BAChB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;8CAAgD;;;;;;8CAGjE,8OAAC,iIAAA,CAAA,QAAK;oCACJ,OAAO;oCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;oCAClD,aAAY;oCACZ,WAAU;oCACV,QAAQ;;;;;;;;;;;;sCAKd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;wCACP,sBAAsB;wCACtB,gBAAgB;wCAChB,mBAAmB;wCACnB,mBAAmB;oCACrB;oCACA,UAAU;oCACV,WAAU;8CACX;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;wCACP,MAAM,OAAY,CAAC;wCACnB,IAAI,iBAAiB,UAAU,KAAK,eAAe,GAAG;wCACtD,IAAI,iBAAiB,YAAY,KAAK,eAAe,GAAG;wCAExD,uBAAuB,mBAAmB,EAAE,EAAE,cAAc;oCAC9D;oCACA,UACE,cACC,iBAAiB,YAAY,CAAC,gBAAgB,IAAI,MAClD,iBAAiB,cAAc,CAAC,gBAAgB,IAAI;oCAEvD,SAAS;oCACT,WACE,iBAAiB,WACb,2CACA,iBAAiB,YACjB,+CACA;;wCAGL,iBAAiB,aAAa;wCAC9B,iBAAiB,YAAY;wCAC7B,iBAAiB,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhD", "debugId": null}}, {"offset": {"line": 5035, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/admin/SupportTicketManagement.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { <PERSON><PERSON>, <PERSON>, CardHeader, <PERSON><PERSON><PERSON>le, CardContent, Modal } from '@/components/ui';\nimport { Grid } from '@/components/layout';\nimport { \n  MessageCircle, \n  Clock, \n  CheckCircle, \n  AlertCircle,\n  Send,\n  Filter,\n  Search,\n  User,\n  Calendar,\n  Tag\n} from 'lucide-react';\nimport { formatDateTime } from '@/lib/utils';\n\ninterface SupportTicket {\n  id: string;\n  subject: string;\n  message: string;\n  status: 'OPEN' | 'IN_PROGRESS' | 'RESOLVED' | 'CLOSED';\n  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';\n  category?: string;\n  createdAt: string;\n  updatedAt: string;\n  user: {\n    id: string;\n    email: string;\n    firstName: string;\n    lastName: string;\n  };\n  responses: Array<{\n    id: string;\n    message: string;\n    isAdmin: boolean;\n    createdAt: string;\n    user?: {\n      id: string;\n      email: string;\n      firstName: string;\n      lastName: string;\n    };\n  }>;\n}\n\nexport const SupportTicketManagement: React.FC = () => {\n  const [tickets, setTickets] = useState<SupportTicket[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [selectedTicket, setSelectedTicket] = useState<SupportTicket | null>(null);\n  const [newResponse, setNewResponse] = useState('');\n  const [submitting, setSubmitting] = useState(false);\n  const [filters, setFilters] = useState({\n    status: '',\n    priority: '',\n    search: '',\n  });\n\n  useEffect(() => {\n    fetchTickets();\n  }, []);\n\n  const fetchTickets = async () => {\n    try {\n      const response = await fetch('/api/admin/support/tickets', {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setTickets(data.data);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch tickets:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const updateTicketStatus = async (ticketId: string, status: string) => {\n    try {\n      const response = await fetch(`/api/admin/support/tickets/${ticketId}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include',\n        body: JSON.stringify({ status }),\n      });\n\n      if (response.ok) {\n        fetchTickets();\n        if (selectedTicket && selectedTicket.id === ticketId) {\n          setSelectedTicket({ ...selectedTicket, status: status as any });\n        }\n      }\n    } catch (error) {\n      console.error('Failed to update ticket status:', error);\n    }\n  };\n\n  const addAdminResponse = async (ticketId: string) => {\n    if (!newResponse.trim()) return;\n\n    setSubmitting(true);\n    try {\n      const response = await fetch(`/api/admin/support/tickets/${ticketId}/responses`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include',\n        body: JSON.stringify({ message: newResponse }),\n      });\n\n      if (response.ok) {\n        setNewResponse('');\n        fetchTickets();\n        // Refresh selected ticket\n        const updatedTickets = await fetch('/api/admin/support/tickets', {\n          credentials: 'include',\n        });\n        if (updatedTickets.ok) {\n          const data = await updatedTickets.json();\n          const updatedTicket = data.data.find((t: SupportTicket) => t.id === ticketId);\n          if (updatedTicket) {\n            setSelectedTicket(updatedTicket);\n          }\n        }\n      }\n    } catch (error) {\n      console.error('Failed to add response:', error);\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'OPEN':\n        return <AlertCircle className=\"h-4 w-4 text-red-500\" />;\n      case 'IN_PROGRESS':\n        return <Clock className=\"h-4 w-4 text-yellow-500\" />;\n      case 'RESOLVED':\n        return <CheckCircle className=\"h-4 w-4 text-green-500\" />;\n      case 'CLOSED':\n        return <CheckCircle className=\"h-4 w-4 text-gray-500\" />;\n      default:\n        return <AlertCircle className=\"h-4 w-4 text-gray-500\" />;\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'OPEN':\n        return 'bg-red-100 text-red-700';\n      case 'IN_PROGRESS':\n        return 'bg-yellow-100 text-yellow-700';\n      case 'RESOLVED':\n        return 'bg-green-100 text-green-700';\n      case 'CLOSED':\n        return 'bg-gray-100 text-gray-700';\n      default:\n        return 'bg-gray-100 text-gray-700';\n    }\n  };\n\n  const getPriorityColor = (priority: string) => {\n    switch (priority) {\n      case 'LOW':\n        return 'bg-blue-100 text-blue-700';\n      case 'MEDIUM':\n        return 'bg-yellow-100 text-yellow-700';\n      case 'HIGH':\n        return 'bg-orange-100 text-orange-700';\n      case 'URGENT':\n        return 'bg-red-100 text-red-700';\n      default:\n        return 'bg-gray-100 text-gray-700';\n    }\n  };\n\n  const filteredTickets = tickets.filter(ticket => {\n    const matchesStatus = !filters.status || ticket.status === filters.status;\n    const matchesPriority = !filters.priority || ticket.priority === filters.priority;\n    const matchesSearch = !filters.search || \n      ticket.subject.toLowerCase().includes(filters.search.toLowerCase()) ||\n      ticket.user.email.toLowerCase().includes(filters.search.toLowerCase()) ||\n      `${ticket.user.firstName} ${ticket.user.lastName}`.toLowerCase().includes(filters.search.toLowerCase());\n    \n    return matchesStatus && matchesPriority && matchesSearch;\n  });\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        {Array.from({ length: 3 }).map((_, i) => (\n          <div key={i} className=\"animate-pulse\">\n            <div className=\"h-32 bg-slate-700 rounded-xl\"></div>\n          </div>\n        ))}\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-slate-800 border border-slate-700 rounded-xl p-6 text-white\">\n        <h1 className=\"text-2xl font-bold mb-2\">Support Ticket Management</h1>\n        <p className=\"text-slate-300\">\n          Review and respond to user support tickets.\n        </p>\n      </div>\n\n      {/* Filters */}\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardContent className=\"p-6\">\n          <div className=\"flex flex-wrap gap-4\">\n            <div className=\"flex-1 min-w-64\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"Search tickets, users...\"\n                  value={filters.search}\n                  onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}\n                  className=\"w-full pl-10 pr-4 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              </div>\n            </div>\n            <select\n              value={filters.status}\n              onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}\n              className=\"px-4 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              <option value=\"\">All Status</option>\n              <option value=\"OPEN\">Open</option>\n              <option value=\"IN_PROGRESS\">In Progress</option>\n              <option value=\"RESOLVED\">Resolved</option>\n              <option value=\"CLOSED\">Closed</option>\n            </select>\n            <select\n              value={filters.priority}\n              onChange={(e) => setFilters(prev => ({ ...prev, priority: e.target.value }))}\n              className=\"px-4 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              <option value=\"\">All Priority</option>\n              <option value=\"LOW\">Low</option>\n              <option value=\"MEDIUM\">Medium</option>\n              <option value=\"HIGH\">High</option>\n              <option value=\"URGENT\">Urgent</option>\n            </select>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Tickets List */}\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2 text-white\">\n            <MessageCircle className=\"h-5 w-5\" />\n            Support Tickets ({filteredTickets.length})\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          {filteredTickets.length > 0 ? (\n            <div className=\"space-y-4\">\n              {filteredTickets.map((ticket) => (\n                <div\n                  key={ticket.id}\n                  onClick={() => setSelectedTicket(ticket)}\n                  className=\"p-4 bg-slate-700 border border-slate-600 rounded-lg hover:bg-slate-600 cursor-pointer transition-colors\"\n                >\n                  <div className=\"flex items-start justify-between mb-3\">\n                    <div className=\"flex-1\">\n                      <h4 className=\"font-medium text-white mb-1\">\n                        {ticket.subject}\n                      </h4>\n                      <div className=\"flex items-center gap-3 text-sm text-slate-400\">\n                        <div className=\"flex items-center gap-1\">\n                          <User className=\"h-3 w-3\" />\n                          {ticket.user.firstName} {ticket.user.lastName} ({ticket.user.email})\n                        </div>\n                        <div className=\"flex items-center gap-1\">\n                          <Calendar className=\"h-3 w-3\" />\n                          {formatDateTime(ticket.createdAt)}\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"flex items-center gap-2 ml-4\">\n                      {getStatusIcon(ticket.status)}\n                      <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(ticket.status)}`}>\n                        {ticket.status}\n                      </span>\n                      <span className={`text-xs px-2 py-1 rounded-full ${getPriorityColor(ticket.priority)}`}>\n                        {ticket.priority}\n                      </span>\n                    </div>\n                  </div>\n                  <p className=\"text-sm text-slate-300 line-clamp-2\">\n                    {ticket.message}\n                  </p>\n                  {ticket.responses.length > 0 && (\n                    <p className=\"text-xs text-slate-400 mt-2\">\n                      {ticket.responses.length} response{ticket.responses.length !== 1 ? 's' : ''}\n                    </p>\n                  )}\n                </div>\n              ))}\n            </div>\n          ) : (\n            <div className=\"text-center py-8\">\n              <MessageCircle className=\"h-12 w-12 text-slate-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium text-white mb-2\">No Support Tickets</h3>\n              <p className=\"text-slate-400\">No tickets match your current filters.</p>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Ticket Detail Modal */}\n      {selectedTicket && (\n        <Modal\n          isOpen={!!selectedTicket}\n          onClose={() => setSelectedTicket(null)}\n          title={`Ticket: ${selectedTicket.subject}`}\n          size=\"xl\"\n        >\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center gap-2\">\n                {getStatusIcon(selectedTicket.status)}\n                <span className={`text-sm px-2 py-1 rounded-full ${getStatusColor(selectedTicket.status)}`}>\n                  {selectedTicket.status}\n                </span>\n                <span className={`text-sm px-2 py-1 rounded-full ${getPriorityColor(selectedTicket.priority)}`}>\n                  {selectedTicket.priority}\n                </span>\n              </div>\n              <div className=\"flex gap-2\">\n                <select\n                  value={selectedTicket.status}\n                  onChange={(e) => updateTicketStatus(selectedTicket.id, e.target.value)}\n                  className=\"px-3 py-1 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                >\n                  <option value=\"OPEN\">Open</option>\n                  <option value=\"IN_PROGRESS\">In Progress</option>\n                  <option value=\"RESOLVED\">Resolved</option>\n                  <option value=\"CLOSED\">Closed</option>\n                </select>\n              </div>\n            </div>\n\n            <div className=\"bg-gray-50 p-4 rounded-lg\">\n              <div className=\"flex items-center gap-2 mb-2\">\n                <User className=\"h-4 w-4 text-gray-500\" />\n                <span className=\"font-medium text-gray-900\">\n                  {selectedTicket.user.firstName} {selectedTicket.user.lastName}\n                </span>\n                <span className=\"text-gray-500\">({selectedTicket.user.email})</span>\n              </div>\n              <p className=\"text-gray-900\">{selectedTicket.message}</p>\n              <p className=\"text-xs text-gray-500 mt-2\">{formatDateTime(selectedTicket.createdAt)}</p>\n            </div>\n\n            {selectedTicket.responses.length > 0 && (\n              <div className=\"space-y-3\">\n                <h4 className=\"font-medium text-gray-900\">Responses:</h4>\n                {selectedTicket.responses.map((response) => (\n                  <div\n                    key={response.id}\n                    className={`p-3 rounded-lg ${\n                      response.isAdmin ? 'bg-blue-50 border-l-4 border-blue-500' : 'bg-gray-50'\n                    }`}\n                  >\n                    <p className=\"text-gray-900\">{response.message}</p>\n                    <p className=\"text-xs text-gray-500 mt-1\">\n                      {response.isAdmin ? 'Admin' : 'User'} • {formatDateTime(response.createdAt)}\n                    </p>\n                  </div>\n                ))}\n              </div>\n            )}\n\n            {selectedTicket.status !== 'CLOSED' && (\n              <div className=\"space-y-3\">\n                <textarea\n                  value={newResponse}\n                  onChange={(e) => setNewResponse(e.target.value)}\n                  placeholder=\"Add admin response...\"\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n                <Button\n                  onClick={() => addAdminResponse(selectedTicket.id)}\n                  disabled={!newResponse.trim() || submitting}\n                  loading={submitting}\n                  className=\"w-full\"\n                >\n                  <Send className=\"h-4 w-4 mr-2\" />\n                  Send Admin Response\n                </Button>\n              </div>\n            )}\n          </div>\n        </Modal>\n      )}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAjBA;;;;;;AAgDO,MAAM,0BAAoC;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IAC3E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,QAAQ;QACR,UAAU;QACV,QAAQ;IACV;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,8BAA8B;gBACzD,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,WAAW,KAAK,IAAI;gBACtB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB,OAAO,UAAkB;QAClD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,2BAA2B,EAAE,UAAU,EAAE;gBACrE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,aAAa;gBACb,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAO;YAChC;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf;gBACA,IAAI,kBAAkB,eAAe,EAAE,KAAK,UAAU;oBACpD,kBAAkB;wBAAE,GAAG,cAAc;wBAAE,QAAQ;oBAAc;gBAC/D;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,YAAY,IAAI,IAAI;QAEzB,cAAc;QACd,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,2BAA2B,EAAE,SAAS,UAAU,CAAC,EAAE;gBAC/E,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,aAAa;gBACb,MAAM,KAAK,SAAS,CAAC;oBAAE,SAAS;gBAAY;YAC9C;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,eAAe;gBACf;gBACA,0BAA0B;gBAC1B,MAAM,iBAAiB,MAAM,MAAM,8BAA8B;oBAC/D,aAAa;gBACf;gBACA,IAAI,eAAe,EAAE,EAAE;oBACrB,MAAM,OAAO,MAAM,eAAe,IAAI;oBACtC,MAAM,gBAAgB,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,IAAqB,EAAE,EAAE,KAAK;oBACpE,IAAI,eAAe;wBACjB,kBAAkB;oBACpB;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,qBAAO,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;QAClC;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAA;QACrC,MAAM,gBAAgB,CAAC,QAAQ,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,MAAM;QACzE,MAAM,kBAAkB,CAAC,QAAQ,QAAQ,IAAI,OAAO,QAAQ,KAAK,QAAQ,QAAQ;QACjF,MAAM,gBAAgB,CAAC,QAAQ,MAAM,IACnC,OAAO,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,MAAM,CAAC,WAAW,OAChE,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,MAAM,CAAC,WAAW,OACnE,GAAG,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,MAAM,CAAC,WAAW;QAEtG,OAAO,iBAAiB,mBAAmB;IAC7C;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACZ,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,8OAAC;oBAAY,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;;;;;mBADP;;;;;;;;;;IAMlB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,8OAAC;wBAAE,WAAU;kCAAiB;;;;;;;;;;;;0BAMhC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO,QAAQ,MAAM;4CACrB,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;oDAAC,CAAC;4CACxE,WAAU;;;;;;;;;;;;;;;;;0CAIhB,8OAAC;gCACC,OAAO,QAAQ,MAAM;gCACrB,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;wCAAC,CAAC;gCACxE,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAG;;;;;;kDACjB,8OAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,8OAAC;wCAAO,OAAM;kDAAc;;;;;;kDAC5B,8OAAC;wCAAO,OAAM;kDAAW;;;;;;kDACzB,8OAAC;wCAAO,OAAM;kDAAS;;;;;;;;;;;;0CAEzB,8OAAC;gCACC,OAAO,QAAQ,QAAQ;gCACvB,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wCAAC,CAAC;gCAC1E,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAG;;;;;;kDACjB,8OAAC;wCAAO,OAAM;kDAAM;;;;;;kDACpB,8OAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,8OAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,8OAAC;wCAAO,OAAM;kDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO/B,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAAY;gCACnB,gBAAgB,MAAM;gCAAC;;;;;;;;;;;;kCAG7C,8OAAC,gIAAA,CAAA,cAAW;kCACT,gBAAgB,MAAM,GAAG,kBACxB,8OAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,uBACpB,8OAAC;oCAEC,SAAS,IAAM,kBAAkB;oCACjC,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEACX,OAAO,OAAO;;;;;;sEAEjB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,kMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;wEACf,OAAO,IAAI,CAAC,SAAS;wEAAC;wEAAE,OAAO,IAAI,CAAC,QAAQ;wEAAC;wEAAG,OAAO,IAAI,CAAC,KAAK;wEAAC;;;;;;;8EAErE,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,0MAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;wEACnB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,SAAS;;;;;;;;;;;;;;;;;;;8DAItC,8OAAC;oDAAI,WAAU;;wDACZ,cAAc,OAAO,MAAM;sEAC5B,8OAAC;4DAAK,WAAW,CAAC,+BAA+B,EAAE,eAAe,OAAO,MAAM,GAAG;sEAC/E,OAAO,MAAM;;;;;;sEAEhB,8OAAC;4DAAK,WAAW,CAAC,+BAA+B,EAAE,iBAAiB,OAAO,QAAQ,GAAG;sEACnF,OAAO,QAAQ;;;;;;;;;;;;;;;;;;sDAItB,8OAAC;4CAAE,WAAU;sDACV,OAAO,OAAO;;;;;;wCAEhB,OAAO,SAAS,CAAC,MAAM,GAAG,mBACzB,8OAAC;4CAAE,WAAU;;gDACV,OAAO,SAAS,CAAC,MAAM;gDAAC;gDAAU,OAAO,SAAS,CAAC,MAAM,KAAK,IAAI,MAAM;;;;;;;;mCAnCxE,OAAO,EAAE;;;;;;;;;iDA0CpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,8OAAC;oCAAE,WAAU;8CAAiB;;;;;;;;;;;;;;;;;;;;;;;YAOrC,gCACC,8OAAC,iIAAA,CAAA,QAAK;gBACJ,QAAQ,CAAC,CAAC;gBACV,SAAS,IAAM,kBAAkB;gBACjC,OAAO,CAAC,QAAQ,EAAE,eAAe,OAAO,EAAE;gBAC1C,MAAK;0BAEL,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;wCACZ,cAAc,eAAe,MAAM;sDACpC,8OAAC;4CAAK,WAAW,CAAC,+BAA+B,EAAE,eAAe,eAAe,MAAM,GAAG;sDACvF,eAAe,MAAM;;;;;;sDAExB,8OAAC;4CAAK,WAAW,CAAC,+BAA+B,EAAE,iBAAiB,eAAe,QAAQ,GAAG;sDAC3F,eAAe,QAAQ;;;;;;;;;;;;8CAG5B,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,OAAO,eAAe,MAAM;wCAC5B,UAAU,CAAC,IAAM,mBAAmB,eAAe,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;wCACrE,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAc;;;;;;0DAC5B,8OAAC;gDAAO,OAAM;0DAAW;;;;;;0DACzB,8OAAC;gDAAO,OAAM;0DAAS;;;;;;;;;;;;;;;;;;;;;;;sCAK7B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;4CAAK,WAAU;;gDACb,eAAe,IAAI,CAAC,SAAS;gDAAC;gDAAE,eAAe,IAAI,CAAC,QAAQ;;;;;;;sDAE/D,8OAAC;4CAAK,WAAU;;gDAAgB;gDAAE,eAAe,IAAI,CAAC,KAAK;gDAAC;;;;;;;;;;;;;8CAE9D,8OAAC;oCAAE,WAAU;8CAAiB,eAAe,OAAO;;;;;;8CACpD,8OAAC;oCAAE,WAAU;8CAA8B,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,eAAe,SAAS;;;;;;;;;;;;wBAGnF,eAAe,SAAS,CAAC,MAAM,GAAG,mBACjC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA4B;;;;;;gCACzC,eAAe,SAAS,CAAC,GAAG,CAAC,CAAC,yBAC7B,8OAAC;wCAEC,WAAW,CAAC,eAAe,EACzB,SAAS,OAAO,GAAG,0CAA0C,cAC7D;;0DAEF,8OAAC;gDAAE,WAAU;0DAAiB,SAAS,OAAO;;;;;;0DAC9C,8OAAC;gDAAE,WAAU;;oDACV,SAAS,OAAO,GAAG,UAAU;oDAAO;oDAAI,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,SAAS;;;;;;;;uCAPvE,SAAS,EAAE;;;;;;;;;;;wBAcvB,eAAe,MAAM,KAAK,0BACzB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,aAAY;oCACZ,MAAM;oCACN,WAAU;;;;;;8CAEZ,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS,IAAM,iBAAiB,eAAe,EAAE;oCACjD,UAAU,CAAC,YAAY,IAAI,MAAM;oCACjC,SAAS;oCACT,WAAU;;sDAEV,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUnD", "debugId": null}}, {"offset": {"line": 5925, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/admin/BinaryPointsManagement.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent, Input, Button } from '@/components/ui';\nimport { \n  ArrowUpDown, \n  Search, \n  Filter, \n  Download, \n  Eye,\n  TrendingUp,\n  TrendingDown,\n  Users,\n  DollarSign\n} from 'lucide-react';\nimport { formatCurrency, formatNumber, formatDateTime } from '@/lib/utils';\n\ninterface BinaryPointsData {\n  id: string;\n  userId: string;\n  user: {\n    id: string;\n    email: string;\n    firstName: string;\n    lastName: string;\n    createdAt: string;\n  };\n  leftPoints: number;\n  rightPoints: number;\n  matchedPoints: number;\n  totalMatched: number;\n  lastMatchDate: string | null;\n  flushDate: string | null;\n  createdAt: string;\n  updatedAt: string;\n}\n\ninterface BinaryMatchHistory {\n  id: string;\n  userId: string;\n  user: {\n    email: string;\n    firstName: string;\n    lastName: string;\n  };\n  matchedPoints: number;\n  payout: number;\n  leftPointsBefore: number;\n  rightPointsBefore: number;\n  leftPointsAfter: number;\n  rightPointsAfter: number;\n  matchDate: string;\n  type: 'WEEKLY' | 'MANUAL';\n}\n\nexport const BinaryPointsManagement: React.FC = () => {\n  const [binaryPointsData, setBinaryPointsData] = useState<BinaryPointsData[]>([]);\n  const [matchHistory, setMatchHistory] = useState<BinaryMatchHistory[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'inactive'>('all');\n  const [selectedUser, setSelectedUser] = useState<string | null>(null);\n  const [showHistory, setShowHistory] = useState(false);\n  const [stats, setStats] = useState({\n    totalUsers: 0,\n    totalLeftPoints: 0,\n    totalRightPoints: 0,\n    totalMatchedPoints: 0,\n    totalPayouts: 0,\n  });\n\n  useEffect(() => {\n    fetchBinaryPointsData();\n    fetchMatchHistory();\n  }, []);\n\n  const fetchBinaryPointsData = async () => {\n    try {\n      const response = await fetch('/api/admin/binary-points', {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setBinaryPointsData(data.data);\n          calculateStats(data.data);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch binary points data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchMatchHistory = async () => {\n    try {\n      const response = await fetch('/api/admin/binary-points/history', {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setMatchHistory(data.data);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch match history:', error);\n    }\n  };\n\n  const calculateStats = (data: BinaryPointsData[]) => {\n    const stats = data.reduce((acc, item) => ({\n      totalUsers: acc.totalUsers + 1,\n      totalLeftPoints: acc.totalLeftPoints + item.leftPoints,\n      totalRightPoints: acc.totalRightPoints + item.rightPoints,\n      totalMatchedPoints: acc.totalMatchedPoints + item.matchedPoints,\n      totalPayouts: acc.totalPayouts + (item.totalMatched * 10), // Assuming $10 per point\n    }), {\n      totalUsers: 0,\n      totalLeftPoints: 0,\n      totalRightPoints: 0,\n      totalMatchedPoints: 0,\n      totalPayouts: 0,\n    });\n\n    setStats(stats);\n  };\n\n  const filteredData = binaryPointsData.filter(item => {\n    const matchesSearch = \n      item.user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      item.user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      item.user.lastName.toLowerCase().includes(searchTerm.toLowerCase());\n\n    const matchesFilter = \n      filterStatus === 'all' ||\n      (filterStatus === 'active' && (item.leftPoints > 0 || item.rightPoints > 0)) ||\n      (filterStatus === 'inactive' && item.leftPoints === 0 && item.rightPoints === 0);\n\n    return matchesSearch && matchesFilter;\n  });\n\n  const exportData = () => {\n    const csvContent = [\n      ['User Email', 'Name', 'Left Points', 'Right Points', 'Matched Points', 'Total Matched', 'Last Match Date'].join(','),\n      ...filteredData.map(item => [\n        item.user.email,\n        `${item.user.firstName} ${item.user.lastName}`,\n        item.leftPoints,\n        item.rightPoints,\n        item.matchedPoints,\n        item.totalMatched,\n        item.lastMatchDate ? formatDateTime(item.lastMatchDate) : 'Never'\n      ].join(','))\n    ].join('\\n');\n\n    const blob = new Blob([csvContent], { type: 'text/csv' });\n    const url = window.URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `binary-points-${new Date().toISOString().split('T')[0]}.csv`;\n    a.click();\n    window.URL.revokeObjectURL(url);\n  };\n\n  const viewUserHistory = (userId: string) => {\n    setSelectedUser(userId);\n    setShowHistory(true);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4\">\n        <Card className=\"bg-slate-800 border-slate-700\">\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm text-slate-400\">Total Users</p>\n                <p className=\"text-2xl font-bold text-white\">{formatNumber(stats.totalUsers)}</p>\n              </div>\n              <Users className=\"h-8 w-8 text-blue-400\" />\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card className=\"bg-slate-800 border-slate-700\">\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm text-slate-400\">Total Left Points</p>\n                <p className=\"text-2xl font-bold text-white\">{formatNumber(stats.totalLeftPoints)}</p>\n              </div>\n              <TrendingUp className=\"h-8 w-8 text-green-400\" />\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card className=\"bg-slate-800 border-slate-700\">\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm text-slate-400\">Total Right Points</p>\n                <p className=\"text-2xl font-bold text-white\">{formatNumber(stats.totalRightPoints)}</p>\n              </div>\n              <TrendingDown className=\"h-8 w-8 text-red-400\" />\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card className=\"bg-slate-800 border-slate-700\">\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm text-slate-400\">Total Matched</p>\n                <p className=\"text-2xl font-bold text-white\">{formatNumber(stats.totalMatchedPoints)}</p>\n              </div>\n              <ArrowUpDown className=\"h-8 w-8 text-purple-400\" />\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card className=\"bg-slate-800 border-slate-700\">\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm text-slate-400\">Total Payouts</p>\n                <p className=\"text-2xl font-bold text-white\">{formatCurrency(stats.totalPayouts)}</p>\n              </div>\n              <DollarSign className=\"h-8 w-8 text-yellow-400\" />\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Main Content */}\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2 text-white\">\n            <ArrowUpDown className=\"h-5 w-5 text-purple-400\" />\n            Binary Points Management\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          {/* Controls */}\n          <div className=\"flex flex-col sm:flex-row gap-4 mb-6\">\n            <div className=\"flex-1\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4\" />\n                <Input\n                  type=\"text\"\n                  placeholder=\"Search by email or name...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400\"\n                />\n              </div>\n            </div>\n            \n            <div className=\"flex gap-2\">\n              <select\n                value={filterStatus}\n                onChange={(e) => setFilterStatus(e.target.value as any)}\n                className=\"px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white text-sm\"\n              >\n                <option value=\"all\">All Users</option>\n                <option value=\"active\">Active Points</option>\n                <option value=\"inactive\">No Points</option>\n              </select>\n              \n              <Button\n                onClick={exportData}\n                className=\"bg-green-600 text-white\"\n              >\n                <Download className=\"h-4 w-4 mr-2\" />\n                Export\n              </Button>\n            </div>\n          </div>\n\n          {/* Table */}\n          <div className=\"overflow-x-auto\">\n            <table className=\"w-full text-sm\">\n              <thead>\n                <tr className=\"border-b border-slate-600\">\n                  <th className=\"text-left py-3 px-4 text-slate-300\">User</th>\n                  <th className=\"text-right py-3 px-4 text-slate-300\">Left Points</th>\n                  <th className=\"text-right py-3 px-4 text-slate-300\">Right Points</th>\n                  <th className=\"text-right py-3 px-4 text-slate-300\">Matchable</th>\n                  <th className=\"text-right py-3 px-4 text-slate-300\">Total Matched</th>\n                  <th className=\"text-right py-3 px-4 text-slate-300\">Last Match</th>\n                  <th className=\"text-center py-3 px-4 text-slate-300\">Actions</th>\n                </tr>\n              </thead>\n              <tbody>\n                {filteredData.map((item) => {\n                  const matchablePoints = Math.min(item.leftPoints, item.rightPoints);\n                  return (\n                    <tr key={item.id} className=\"border-b border-slate-700\">\n                      <td className=\"py-3 px-4\">\n                        <div>\n                          <div className=\"font-medium text-white\">\n                            {item.user.firstName} {item.user.lastName}\n                          </div>\n                          <div className=\"text-sm text-slate-400\">{item.user.email}</div>\n                        </div>\n                      </td>\n                      <td className=\"text-right py-3 px-4 text-white\">\n                        {formatNumber(item.leftPoints)}\n                      </td>\n                      <td className=\"text-right py-3 px-4 text-white\">\n                        {formatNumber(item.rightPoints)}\n                      </td>\n                      <td className=\"text-right py-3 px-4\">\n                        <span className={`font-medium ${matchablePoints > 0 ? 'text-green-400' : 'text-slate-400'}`}>\n                          {formatNumber(matchablePoints)}\n                        </span>\n                      </td>\n                      <td className=\"text-right py-3 px-4 text-white\">\n                        {formatNumber(item.totalMatched)}\n                      </td>\n                      <td className=\"text-right py-3 px-4 text-slate-300\">\n                        {item.lastMatchDate ? formatDateTime(item.lastMatchDate) : 'Never'}\n                      </td>\n                      <td className=\"text-center py-3 px-4\">\n                        <Button\n                          onClick={() => viewUserHistory(item.userId)}\n                          className=\"bg-blue-600 text-white text-xs px-2 py-1\"\n                        >\n                          <Eye className=\"h-3 w-3 mr-1\" />\n                          View\n                        </Button>\n                      </td>\n                    </tr>\n                  );\n                })}\n              </tbody>\n            </table>\n          </div>\n\n          {filteredData.length === 0 && (\n            <div className=\"text-center py-8 text-slate-400\">\n              No binary points data found matching your criteria.\n            </div>\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAfA;;;;;;AAuDO,MAAM,yBAAmC;IAC9C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IAC/E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB,EAAE;IACzE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiC;IAChF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACjC,YAAY;QACZ,iBAAiB;QACjB,kBAAkB;QAClB,oBAAoB;QACpB,cAAc;IAChB;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA;IACF,GAAG,EAAE;IAEL,MAAM,wBAAwB;QAC5B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,4BAA4B;gBACvD,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,oBAAoB,KAAK,IAAI;oBAC7B,eAAe,KAAK,IAAI;gBAC1B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;QACvD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,oCAAoC;gBAC/D,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,gBAAgB,KAAK,IAAI;gBAC3B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,QAAQ,KAAK,MAAM,CAAC,CAAC,KAAK,OAAS,CAAC;gBACxC,YAAY,IAAI,UAAU,GAAG;gBAC7B,iBAAiB,IAAI,eAAe,GAAG,KAAK,UAAU;gBACtD,kBAAkB,IAAI,gBAAgB,GAAG,KAAK,WAAW;gBACzD,oBAAoB,IAAI,kBAAkB,GAAG,KAAK,aAAa;gBAC/D,cAAc,IAAI,YAAY,GAAI,KAAK,YAAY,GAAG;YACxD,CAAC,GAAG;YACF,YAAY;YACZ,iBAAiB;YACjB,kBAAkB;YAClB,oBAAoB;YACpB,cAAc;QAChB;QAEA,SAAS;IACX;IAEA,MAAM,eAAe,iBAAiB,MAAM,CAAC,CAAA;QAC3C,MAAM,gBACJ,KAAK,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC7D,KAAK,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACjE,KAAK,IAAI,CAAC,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAElE,MAAM,gBACJ,iBAAiB,SAChB,iBAAiB,YAAY,CAAC,KAAK,UAAU,GAAG,KAAK,KAAK,WAAW,GAAG,CAAC,KACzE,iBAAiB,cAAc,KAAK,UAAU,KAAK,KAAK,KAAK,WAAW,KAAK;QAEhF,OAAO,iBAAiB;IAC1B;IAEA,MAAM,aAAa;QACjB,MAAM,aAAa;YACjB;gBAAC;gBAAc;gBAAQ;gBAAe;gBAAgB;gBAAkB;gBAAiB;aAAkB,CAAC,IAAI,CAAC;eAC9G,aAAa,GAAG,CAAC,CAAA,OAAQ;oBAC1B,KAAK,IAAI,CAAC,KAAK;oBACf,GAAG,KAAK,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,QAAQ,EAAE;oBAC9C,KAAK,UAAU;oBACf,KAAK,WAAW;oBAChB,KAAK,aAAa;oBAClB,KAAK,YAAY;oBACjB,KAAK,aAAa,GAAG,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,aAAa,IAAI;iBAC3D,CAAC,IAAI,CAAC;SACR,CAAC,IAAI,CAAC;QAEP,MAAM,OAAO,IAAI,KAAK;YAAC;SAAW,EAAE;YAAE,MAAM;QAAW;QACvD,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;QACvC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG,CAAC,cAAc,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;QAC1E,EAAE,KAAK;QACP,OAAO,GAAG,CAAC,eAAe,CAAC;IAC7B;IAEA,MAAM,kBAAkB,CAAC;QACvB,gBAAgB;QAChB,eAAe;IACjB;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAyB;;;;;;0DACtC,8OAAC;gDAAE,WAAU;0DAAiC,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,MAAM,UAAU;;;;;;;;;;;;kDAE7E,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAKvB,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAyB;;;;;;0DACtC,8OAAC;gDAAE,WAAU;0DAAiC,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,MAAM,eAAe;;;;;;;;;;;;kDAElF,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK5B,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAyB;;;;;;0DACtC,8OAAC;gDAAE,WAAU;0DAAiC,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,MAAM,gBAAgB;;;;;;;;;;;;kDAEnF,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK9B,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAyB;;;;;;0DACtC,8OAAC;gDAAE,WAAU;0DAAiC,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,MAAM,kBAAkB;;;;;;;;;;;;kDAErF,8OAAC,wNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK7B,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAyB;;;;;;0DACtC,8OAAC;gDAAE,WAAU;0DAAiC,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,YAAY;;;;;;;;;;;;kDAEjF,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO9B,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,wNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCAA4B;;;;;;;;;;;;kCAIvD,8OAAC,gIAAA,CAAA,cAAW;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,WAAU;;;;;;;;;;;;;;;;;kDAKhB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDAC/C,WAAU;;kEAEV,8OAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,8OAAC;wDAAO,OAAM;kEAAS;;;;;;kEACvB,8OAAC;wDAAO,OAAM;kEAAW;;;;;;;;;;;;0DAG3B,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS;gDACT,WAAU;;kEAEV,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;0CAO3C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;sDACC,cAAA,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;kEAAqC;;;;;;kEACnD,8OAAC;wDAAG,WAAU;kEAAsC;;;;;;kEACpD,8OAAC;wDAAG,WAAU;kEAAsC;;;;;;kEACpD,8OAAC;wDAAG,WAAU;kEAAsC;;;;;;kEACpD,8OAAC;wDAAG,WAAU;kEAAsC;;;;;;kEACpD,8OAAC;wDAAG,WAAU;kEAAsC;;;;;;kEACpD,8OAAC;wDAAG,WAAU;kEAAuC;;;;;;;;;;;;;;;;;sDAGzD,8OAAC;sDACE,aAAa,GAAG,CAAC,CAAC;gDACjB,MAAM,kBAAkB,KAAK,GAAG,CAAC,KAAK,UAAU,EAAE,KAAK,WAAW;gDAClE,qBACE,8OAAC;oDAAiB,WAAU;;sEAC1B,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;;kFACC,8OAAC;wEAAI,WAAU;;4EACZ,KAAK,IAAI,CAAC,SAAS;4EAAC;4EAAE,KAAK,IAAI,CAAC,QAAQ;;;;;;;kFAE3C,8OAAC;wEAAI,WAAU;kFAA0B,KAAK,IAAI,CAAC,KAAK;;;;;;;;;;;;;;;;;sEAG5D,8OAAC;4DAAG,WAAU;sEACX,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,UAAU;;;;;;sEAE/B,8OAAC;4DAAG,WAAU;sEACX,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,WAAW;;;;;;sEAEhC,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAK,WAAW,CAAC,YAAY,EAAE,kBAAkB,IAAI,mBAAmB,kBAAkB;0EACxF,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE;;;;;;;;;;;sEAGlB,8OAAC;4DAAG,WAAU;sEACX,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,YAAY;;;;;;sEAEjC,8OAAC;4DAAG,WAAU;sEACX,KAAK,aAAa,GAAG,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,aAAa,IAAI;;;;;;sEAE7D,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC,kIAAA,CAAA,SAAM;gEACL,SAAS,IAAM,gBAAgB,KAAK,MAAM;gEAC1C,WAAU;;kFAEV,8OAAC,gMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;mDA/B7B,KAAK,EAAE;;;;;4CAqCpB;;;;;;;;;;;;;;;;;4BAKL,aAAa,MAAM,KAAK,mBACvB,8OAAC;gCAAI,WAAU;0CAAkC;;;;;;;;;;;;;;;;;;;;;;;;AAQ7D", "debugId": null}}, {"offset": {"line": 6735, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/admin/ReferralCommissionTracking.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardContent, Input, Button } from '@/components/ui';\nimport { \n  Users, \n  Search, \n  Filter, \n  Download, \n  Eye,\n  TrendingUp,\n  DollarSign,\n  Calendar,\n  ArrowRight\n} from 'lucide-react';\nimport { formatCurrency, formatNumber, formatDateTime } from '@/lib/utils';\n\ninterface ReferralCommission {\n  id: string;\n  fromUserId: string;\n  toUserId: string;\n  fromUser: {\n    id: string;\n    email: string;\n    firstName: string;\n    lastName: string;\n  };\n  toUser: {\n    id: string;\n    email: string;\n    firstName: string;\n    lastName: string;\n  };\n  amount: number;\n  commissionRate: number;\n  originalAmount: number;\n  type: 'DIRECT_REFERRAL' | 'MINING_PURCHASE';\n  description: string;\n  createdAt: string;\n  status: 'COMPLETED' | 'PENDING' | 'FAILED';\n}\n\ninterface CommissionStats {\n  totalCommissions: number;\n  totalAmount: number;\n  averageCommission: number;\n  topEarners: Array<{\n    userId: string;\n    user: {\n      email: string;\n      firstName: string;\n      lastName: string;\n    };\n    totalEarned: number;\n    commissionCount: number;\n  }>;\n  recentActivity: number;\n}\n\nexport const ReferralCommissionTracking: React.FC = () => {\n  const [commissions, setCommissions] = useState<ReferralCommission[]>([]);\n  const [stats, setStats] = useState<CommissionStats | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterType, setFilterType] = useState<'all' | 'DIRECT_REFERRAL' | 'MINING_PURCHASE'>('all');\n  const [filterStatus, setFilterStatus] = useState<'all' | 'COMPLETED' | 'PENDING' | 'FAILED'>('all');\n  const [dateRange, setDateRange] = useState<'7d' | '30d' | '90d' | 'all'>('30d');\n  const [selectedUser, setSelectedUser] = useState<string | null>(null);\n\n  useEffect(() => {\n    fetchCommissions();\n    fetchStats();\n  }, [dateRange, filterType, filterStatus]);\n\n  const fetchCommissions = async () => {\n    try {\n      const params = new URLSearchParams({\n        dateRange,\n        type: filterType,\n        status: filterStatus,\n        limit: '100',\n      });\n\n      const response = await fetch(`/api/admin/referral-commissions?${params}`, {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setCommissions(data.data);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch referral commissions:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchStats = async () => {\n    try {\n      const params = new URLSearchParams({\n        dateRange,\n      });\n\n      const response = await fetch(`/api/admin/referral-commissions/stats?${params}`, {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setStats(data.data);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch commission stats:', error);\n    }\n  };\n\n  const filteredCommissions = commissions.filter(commission => {\n    const matchesSearch = \n      commission.fromUser.email.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      commission.fromUser.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      commission.fromUser.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      commission.toUser.email.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      commission.toUser.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      commission.toUser.lastName.toLowerCase().includes(searchTerm.toLowerCase());\n\n    return matchesSearch;\n  });\n\n  const exportData = () => {\n    const csvContent = [\n      ['Date', 'From User', 'To User', 'Type', 'Original Amount', 'Commission Rate', 'Commission Amount', 'Status'].join(','),\n      ...filteredCommissions.map(commission => [\n        formatDateTime(commission.createdAt),\n        `${commission.fromUser.firstName} ${commission.fromUser.lastName} (${commission.fromUser.email})`,\n        `${commission.toUser.firstName} ${commission.toUser.lastName} (${commission.toUser.email})`,\n        commission.type,\n        commission.originalAmount,\n        `${commission.commissionRate}%`,\n        commission.amount,\n        commission.status\n      ].join(','))\n    ].join('\\n');\n\n    const blob = new Blob([csvContent], { type: 'text/csv' });\n    const url = window.URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `referral-commissions-${new Date().toISOString().split('T')[0]}.csv`;\n    a.click();\n    window.URL.revokeObjectURL(url);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Stats Cards */}\n      {stats && (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n          <Card className=\"bg-slate-800 border-slate-700\">\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm text-slate-400\">Total Commissions</p>\n                  <p className=\"text-2xl font-bold text-white\">{formatNumber(stats.totalCommissions)}</p>\n                </div>\n                <Users className=\"h-8 w-8 text-blue-400\" />\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"bg-slate-800 border-slate-700\">\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm text-slate-400\">Total Amount</p>\n                  <p className=\"text-2xl font-bold text-white\">{formatCurrency(stats.totalAmount)}</p>\n                </div>\n                <DollarSign className=\"h-8 w-8 text-green-400\" />\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"bg-slate-800 border-slate-700\">\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm text-slate-400\">Average Commission</p>\n                  <p className=\"text-2xl font-bold text-white\">{formatCurrency(stats.averageCommission)}</p>\n                </div>\n                <TrendingUp className=\"h-8 w-8 text-orange-400\" />\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"bg-slate-800 border-slate-700\">\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm text-slate-400\">Recent Activity</p>\n                  <p className=\"text-2xl font-bold text-white\">{formatNumber(stats.recentActivity)}</p>\n                </div>\n                <Calendar className=\"h-8 w-8 text-purple-400\" />\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      )}\n\n      {/* Top Earners */}\n      {stats?.topEarners && stats.topEarners.length > 0 && (\n        <Card className=\"bg-slate-800 border-slate-700\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2 text-white\">\n              <TrendingUp className=\"h-5 w-5 text-orange-400\" />\n              Top Commission Earners\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n              {stats.topEarners.slice(0, 6).map((earner, index) => (\n                <div key={earner.userId} className=\"p-4 bg-slate-700 rounded-lg\">\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <span className=\"text-sm font-medium text-slate-300\">#{index + 1}</span>\n                    <span className=\"text-lg font-bold text-green-400\">\n                      {formatCurrency(earner.totalEarned)}\n                    </span>\n                  </div>\n                  <div className=\"text-sm text-white\">\n                    {earner.user.firstName} {earner.user.lastName}\n                  </div>\n                  <div className=\"text-xs text-slate-400\">{earner.user.email}</div>\n                  <div className=\"text-xs text-slate-400 mt-1\">\n                    {earner.commissionCount} commissions\n                  </div>\n                </div>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Main Content */}\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2 text-white\">\n            <Users className=\"h-5 w-5 text-blue-400\" />\n            Referral Commission Tracking\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          {/* Controls */}\n          <div className=\"flex flex-col lg:flex-row gap-4 mb-6\">\n            <div className=\"flex-1\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4\" />\n                <Input\n                  type=\"text\"\n                  placeholder=\"Search by user email or name...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400\"\n                />\n              </div>\n            </div>\n            \n            <div className=\"flex gap-2\">\n              <select\n                value={dateRange}\n                onChange={(e) => setDateRange(e.target.value as any)}\n                className=\"px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white text-sm\"\n              >\n                <option value=\"7d\">Last 7 days</option>\n                <option value=\"30d\">Last 30 days</option>\n                <option value=\"90d\">Last 90 days</option>\n                <option value=\"all\">All time</option>\n              </select>\n\n              <select\n                value={filterType}\n                onChange={(e) => setFilterType(e.target.value as any)}\n                className=\"px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white text-sm\"\n              >\n                <option value=\"all\">All Types</option>\n                <option value=\"DIRECT_REFERRAL\">Direct Referral</option>\n                <option value=\"MINING_PURCHASE\">Mining Purchase</option>\n              </select>\n\n              <select\n                value={filterStatus}\n                onChange={(e) => setFilterStatus(e.target.value as any)}\n                className=\"px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white text-sm\"\n              >\n                <option value=\"all\">All Status</option>\n                <option value=\"COMPLETED\">Completed</option>\n                <option value=\"PENDING\">Pending</option>\n                <option value=\"FAILED\">Failed</option>\n              </select>\n              \n              <Button\n                onClick={exportData}\n                className=\"bg-green-600 hover:bg-green-700 text-white\"\n              >\n                <Download className=\"h-4 w-4 mr-2\" />\n                Export\n              </Button>\n            </div>\n          </div>\n\n          {/* Table */}\n          <div className=\"overflow-x-auto\">\n            <table className=\"w-full text-sm\">\n              <thead>\n                <tr className=\"border-b border-slate-600\">\n                  <th className=\"text-left py-3 px-4 text-slate-300\">Date</th>\n                  <th className=\"text-left py-3 px-4 text-slate-300\">From → To</th>\n                  <th className=\"text-left py-3 px-4 text-slate-300\">Type</th>\n                  <th className=\"text-right py-3 px-4 text-slate-300\">Original Amount</th>\n                  <th className=\"text-right py-3 px-4 text-slate-300\">Rate</th>\n                  <th className=\"text-right py-3 px-4 text-slate-300\">Commission</th>\n                  <th className=\"text-center py-3 px-4 text-slate-300\">Status</th>\n                </tr>\n              </thead>\n              <tbody>\n                {filteredCommissions.map((commission) => (\n                  <tr key={commission.id} className=\"border-b border-slate-700 hover:bg-slate-700/50\">\n                    <td className=\"py-3 px-4 text-slate-300\">\n                      {formatDateTime(commission.createdAt)}\n                    </td>\n                    <td className=\"py-3 px-4\">\n                      <div className=\"flex items-center gap-2\">\n                        <div className=\"text-xs\">\n                          <div className=\"text-white\">\n                            {commission.fromUser.firstName} {commission.fromUser.lastName}\n                          </div>\n                          <div className=\"text-slate-400\">{commission.fromUser.email}</div>\n                        </div>\n                        <ArrowRight className=\"h-3 w-3 text-slate-400\" />\n                        <div className=\"text-xs\">\n                          <div className=\"text-white\">\n                            {commission.toUser.firstName} {commission.toUser.lastName}\n                          </div>\n                          <div className=\"text-slate-400\">{commission.toUser.email}</div>\n                        </div>\n                      </div>\n                    </td>\n                    <td className=\"py-3 px-4\">\n                      <span className={`px-2 py-1 rounded text-xs ${\n                        commission.type === 'DIRECT_REFERRAL' \n                          ? 'bg-blue-600/20 text-blue-400' \n                          : 'bg-green-600/20 text-green-400'\n                      }`}>\n                        {commission.type === 'DIRECT_REFERRAL' ? 'Referral' : 'Mining'}\n                      </span>\n                    </td>\n                    <td className=\"text-right py-3 px-4 text-white\">\n                      {formatCurrency(commission.originalAmount)}\n                    </td>\n                    <td className=\"text-right py-3 px-4 text-slate-300\">\n                      {commission.commissionRate}%\n                    </td>\n                    <td className=\"text-right py-3 px-4 text-green-400 font-medium\">\n                      {formatCurrency(commission.amount)}\n                    </td>\n                    <td className=\"text-center py-3 px-4\">\n                      <span className={`px-2 py-1 rounded text-xs ${\n                        commission.status === 'COMPLETED' \n                          ? 'bg-green-600/20 text-green-400' \n                          : commission.status === 'PENDING'\n                          ? 'bg-yellow-600/20 text-yellow-400'\n                          : 'bg-red-600/20 text-red-400'\n                      }`}>\n                        {commission.status}\n                      </span>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n\n          {filteredCommissions.length === 0 && (\n            <div className=\"text-center py-8 text-slate-400\">\n              No referral commissions found matching your criteria.\n            </div>\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAfA;;;;;;AA2DO,MAAM,6BAAuC;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB,EAAE;IACvE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiD;IAC5F,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8C;IAC7F,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgC;IACzE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA;IACF,GAAG;QAAC;QAAW;QAAY;KAAa;IAExC,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,SAAS,IAAI,gBAAgB;gBACjC;gBACA,MAAM;gBACN,QAAQ;gBACR,OAAO;YACT;YAEA,MAAM,WAAW,MAAM,MAAM,CAAC,gCAAgC,EAAE,QAAQ,EAAE;gBACxE,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,eAAe,KAAK,IAAI;gBAC1B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;QACzD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,SAAS,IAAI,gBAAgB;gBACjC;YACF;YAEA,MAAM,WAAW,MAAM,MAAM,CAAC,sCAAsC,EAAE,QAAQ,EAAE;gBAC9E,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,SAAS,KAAK,IAAI;gBACpB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD;IACF;IAEA,MAAM,sBAAsB,YAAY,MAAM,CAAC,CAAA;QAC7C,MAAM,gBACJ,WAAW,QAAQ,CAAC,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACvE,WAAW,QAAQ,CAAC,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC3E,WAAW,QAAQ,CAAC,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC1E,WAAW,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACrE,WAAW,MAAM,CAAC,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzE,WAAW,MAAM,CAAC,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAE1E,OAAO;IACT;IAEA,MAAM,aAAa;QACjB,MAAM,aAAa;YACjB;gBAAC;gBAAQ;gBAAa;gBAAW;gBAAQ;gBAAmB;gBAAmB;gBAAqB;aAAS,CAAC,IAAI,CAAC;eAChH,oBAAoB,GAAG,CAAC,CAAA,aAAc;oBACvC,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,SAAS;oBACnC,GAAG,WAAW,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,WAAW,QAAQ,CAAC,QAAQ,CAAC,EAAE,EAAE,WAAW,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;oBACjG,GAAG,WAAW,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,WAAW,MAAM,CAAC,QAAQ,CAAC,EAAE,EAAE,WAAW,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;oBAC3F,WAAW,IAAI;oBACf,WAAW,cAAc;oBACzB,GAAG,WAAW,cAAc,CAAC,CAAC,CAAC;oBAC/B,WAAW,MAAM;oBACjB,WAAW,MAAM;iBAClB,CAAC,IAAI,CAAC;SACR,CAAC,IAAI,CAAC;QAEP,MAAM,OAAO,IAAI,KAAK;YAAC;SAAW,EAAE;YAAE,MAAM;QAAW;QACvD,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;QACvC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG,CAAC,qBAAqB,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;QACjF,EAAE,KAAK;QACP,OAAO,GAAG,CAAC,eAAe,CAAC;IAC7B;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;YAEZ,uBACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAyB;;;;;;0DACtC,8OAAC;gDAAE,WAAU;0DAAiC,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,MAAM,gBAAgB;;;;;;;;;;;;kDAEnF,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAKvB,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAyB;;;;;;0DACtC,8OAAC;gDAAE,WAAU;0DAAiC,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,WAAW;;;;;;;;;;;;kDAEhF,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK5B,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAyB;;;;;;0DACtC,8OAAC;gDAAE,WAAU;0DAAiC,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,iBAAiB;;;;;;;;;;;;kDAEtF,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK5B,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAyB;;;;;;0DACtC,8OAAC;gDAAE,WAAU;0DAAiC,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,MAAM,cAAc;;;;;;;;;;;;kDAEjF,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQ7B,OAAO,cAAc,MAAM,UAAU,CAAC,MAAM,GAAG,mBAC9C,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAA4B;;;;;;;;;;;;kCAItD,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;sCACZ,MAAM,UAAU,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAQ,sBACzC,8OAAC;oCAAwB,WAAU;;sDACjC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;;wDAAqC;wDAAE,QAAQ;;;;;;;8DAC/D,8OAAC;oDAAK,WAAU;8DACb,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,WAAW;;;;;;;;;;;;sDAGtC,8OAAC;4CAAI,WAAU;;gDACZ,OAAO,IAAI,CAAC,SAAS;gDAAC;gDAAE,OAAO,IAAI,CAAC,QAAQ;;;;;;;sDAE/C,8OAAC;4CAAI,WAAU;sDAA0B,OAAO,IAAI,CAAC,KAAK;;;;;;sDAC1D,8OAAC;4CAAI,WAAU;;gDACZ,OAAO,eAAe;gDAAC;;;;;;;;mCAZlB,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;0BAsBjC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAA0B;;;;;;;;;;;;kCAI/C,8OAAC,gIAAA,CAAA,cAAW;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,WAAU;;;;;;;;;;;;;;;;;kDAKhB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;gDAC5C,WAAU;;kEAEV,8OAAC;wDAAO,OAAM;kEAAK;;;;;;kEACnB,8OAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,8OAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,8OAAC;wDAAO,OAAM;kEAAM;;;;;;;;;;;;0DAGtB,8OAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;kEAEV,8OAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,8OAAC;wDAAO,OAAM;kEAAkB;;;;;;kEAChC,8OAAC;wDAAO,OAAM;kEAAkB;;;;;;;;;;;;0DAGlC,8OAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDAC/C,WAAU;;kEAEV,8OAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,8OAAC;wDAAO,OAAM;kEAAY;;;;;;kEAC1B,8OAAC;wDAAO,OAAM;kEAAU;;;;;;kEACxB,8OAAC;wDAAO,OAAM;kEAAS;;;;;;;;;;;;0DAGzB,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS;gDACT,WAAU;;kEAEV,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;0CAO3C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;sDACC,cAAA,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;kEAAqC;;;;;;kEACnD,8OAAC;wDAAG,WAAU;kEAAqC;;;;;;kEACnD,8OAAC;wDAAG,WAAU;kEAAqC;;;;;;kEACnD,8OAAC;wDAAG,WAAU;kEAAsC;;;;;;kEACpD,8OAAC;wDAAG,WAAU;kEAAsC;;;;;;kEACpD,8OAAC;wDAAG,WAAU;kEAAsC;;;;;;kEACpD,8OAAC;wDAAG,WAAU;kEAAuC;;;;;;;;;;;;;;;;;sDAGzD,8OAAC;sDACE,oBAAoB,GAAG,CAAC,CAAC,2BACxB,8OAAC;oDAAuB,WAAU;;sEAChC,8OAAC;4DAAG,WAAU;sEACX,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,SAAS;;;;;;sEAEtC,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;;oFACZ,WAAW,QAAQ,CAAC,SAAS;oFAAC;oFAAE,WAAW,QAAQ,CAAC,QAAQ;;;;;;;0FAE/D,8OAAC;gFAAI,WAAU;0FAAkB,WAAW,QAAQ,CAAC,KAAK;;;;;;;;;;;;kFAE5D,8OAAC,kNAAA,CAAA,aAAU;wEAAC,WAAU;;;;;;kFACtB,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;;oFACZ,WAAW,MAAM,CAAC,SAAS;oFAAC;oFAAE,WAAW,MAAM,CAAC,QAAQ;;;;;;;0FAE3D,8OAAC;gFAAI,WAAU;0FAAkB,WAAW,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;sEAI9D,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAK,WAAW,CAAC,0BAA0B,EAC1C,WAAW,IAAI,KAAK,oBAChB,iCACA,kCACJ;0EACC,WAAW,IAAI,KAAK,oBAAoB,aAAa;;;;;;;;;;;sEAG1D,8OAAC;4DAAG,WAAU;sEACX,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,cAAc;;;;;;sEAE3C,8OAAC;4DAAG,WAAU;;gEACX,WAAW,cAAc;gEAAC;;;;;;;sEAE7B,8OAAC;4DAAG,WAAU;sEACX,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,MAAM;;;;;;sEAEnC,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAK,WAAW,CAAC,0BAA0B,EAC1C,WAAW,MAAM,KAAK,cAClB,mCACA,WAAW,MAAM,KAAK,YACtB,qCACA,8BACJ;0EACC,WAAW,MAAM;;;;;;;;;;;;mDA/Cf,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;4BAwD7B,oBAAoB,MAAM,KAAK,mBAC9B,8OAAC;gCAAI,WAAU;0CAAkC;;;;;;;;;;;;;;;;;;;;;;;;AAQ7D", "debugId": null}}, {"offset": {"line": 7712, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/admin/SystemLogs.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Card, CardHeader, CardTitle, CardContent, Button, Input } from '@/components/ui';\nimport { \n  FileText, \n  Search, \n  Filter, \n  Download,\n  Calendar,\n  User,\n  Activity,\n  AlertTriangle,\n  Info,\n  CheckCircle,\n  XCircle\n} from 'lucide-react';\nimport { formatDate } from '@/lib/utils';\n\ninterface SystemLog {\n  id: string;\n  action: string;\n  userId?: string;\n  user?: {\n    firstName: string;\n    lastName: string;\n    email: string;\n  };\n  details: any;\n  ipAddress: string;\n  userAgent: string;\n  createdAt: string;\n}\n\nexport const SystemLogs: React.FC = () => {\n  const [logs, setLogs] = useState<SystemLog[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterAction, setFilterAction] = useState<string>('all');\n  const [dateRange, setDateRange] = useState<'today' | 'week' | 'month' | 'all'>('today');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n\n  useEffect(() => {\n    fetchLogs();\n  }, [currentPage, searchTerm, filterAction, dateRange]);\n\n  const fetchLogs = async () => {\n    try {\n      setLoading(true);\n      const params = new URLSearchParams({\n        page: currentPage.toString(),\n        limit: '50',\n        search: searchTerm,\n        action: filterAction,\n        dateRange,\n      });\n\n      const response = await fetch(`/api/admin/logs?${params}`, {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setLogs(data.logs);\n          setTotalPages(data.totalPages);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch logs:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const exportLogs = async () => {\n    try {\n      const params = new URLSearchParams({\n        search: searchTerm,\n        action: filterAction,\n        dateRange,\n        export: 'true',\n      });\n\n      const response = await fetch(`/api/admin/logs/export?${params}`, {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const blob = await response.blob();\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = `system-logs-${new Date().toISOString().split('T')[0]}.csv`;\n        document.body.appendChild(a);\n        a.click();\n        window.URL.revokeObjectURL(url);\n        document.body.removeChild(a);\n      }\n    } catch (error) {\n      console.error('Failed to export logs:', error);\n    }\n  };\n\n  const getActionIcon = (action: string) => {\n    switch (action) {\n      case 'USER_LOGIN':\n      case 'USER_LOGOUT':\n        return <User className=\"h-4 w-4 text-blue-400\" />;\n      case 'USER_REGISTER':\n        return <CheckCircle className=\"h-4 w-4 text-green-400\" />;\n      case 'MINING_PURCHASE':\n      case 'WITHDRAWAL_REQUEST':\n        return <Activity className=\"h-4 w-4 text-purple-400\" />;\n      case 'KYC_SUBMIT':\n      case 'KYC_APPROVE':\n      case 'KYC_REJECT':\n        return <FileText className=\"h-4 w-4 text-orange-400\" />;\n      case 'ADMIN_ACTION':\n        return <AlertTriangle className=\"h-4 w-4 text-red-400\" />;\n      default:\n        return <Info className=\"h-4 w-4 text-slate-400\" />;\n    }\n  };\n\n  const getActionColor = (action: string) => {\n    switch (action) {\n      case 'USER_LOGIN':\n      case 'USER_REGISTER':\n      case 'KYC_APPROVE':\n        return 'text-green-300 bg-green-900/20 border border-green-700';\n      case 'USER_LOGOUT':\n        return 'text-blue-300 bg-blue-900/20 border border-blue-700';\n      case 'MINING_PURCHASE':\n      case 'WITHDRAWAL_REQUEST':\n        return 'text-purple-300 bg-purple-900/20 border border-purple-700';\n      case 'KYC_SUBMIT':\n        return 'text-orange-300 bg-orange-900/20 border border-orange-700';\n      case 'KYC_REJECT':\n      case 'ADMIN_ACTION':\n        return 'text-red-300 bg-red-900/20 border border-red-700';\n      default:\n        return 'text-slate-300 bg-slate-700 border border-slate-600';\n    }\n  };\n\n  const parseLogDetails = (details: any) => {\n    if (!details) return null;\n\n    try {\n      // If it's already an object, return it\n      if (typeof details === 'object') return details;\n\n      // If it's a string, try to parse it as JSON\n      if (typeof details === 'string') {\n        return JSON.parse(details);\n      }\n\n      return details;\n    } catch {\n      // If parsing fails, return the original string\n      return details;\n    }\n  };\n\n  const formatLogDetails = (details: any) => {\n    const parsed = parseLogDetails(details);\n\n    if (!parsed) return 'No details available';\n\n    if (typeof parsed === 'string') return parsed;\n\n    if (typeof parsed === 'object') {\n      // Handle specific log types with better formatting\n      if (parsed.type === 'CREDIT' || parsed.type === 'DEBIT') {\n        return (\n          <div className=\"space-y-1\">\n            <div><span className=\"text-slate-400\">Type:</span> <span className=\"text-white\">{String(parsed.type)}</span></div>\n            <div><span className=\"text-slate-400\">Amount:</span> <span className=\"text-green-400\">${String(parsed.amount)}</span></div>\n            <div><span className=\"text-slate-400\">Reason:</span> <span className=\"text-white\">{String(parsed.reason)}</span></div>\n            {parsed.description && <div><span className=\"text-slate-400\">Description:</span> <span className=\"text-white\">{String(parsed.description)}</span></div>}            \n          </div>\n        );\n      }\n\n      // Handle targetUser object specifically\n      if (parsed.targetUser && typeof parsed.targetUser === 'object') {\n        return (\n          <div className=\"space-y-1\">\n            <div><span className=\"text-slate-400\">Type:</span> <span className=\"text-white\">{String(parsed.type)}</span></div>\n            <div><span className=\"text-slate-400\">Amount:</span> <span className=\"text-green-400\">${String(parsed.amount)}</span></div>\n            {parsed.reason && <div><span className=\"text-slate-400\">Reason:</span> <span className=\"text-white\">{String(parsed.reason)}</span></div>}\n            {parsed.description && <div><span className=\"text-slate-400\">Description:</span> <span className=\"text-white\">{String(parsed.description)}</span></div>}\n            {parsed.previousBalance !== undefined && <div><span className=\"text-slate-400\">Previous Balance:</span> <span className=\"text-slate-300\">${String(parsed.previousBalance)}</span></div>}\n            {parsed.newBalance !== undefined && <div><span className=\"text-slate-400\">New Balance:</span> <span className=\"text-slate-300\">${String(parsed.newBalance)}</span></div>}\n          </div>\n        );\n      }\n\n      if (parsed.targetUserId || parsed.targetUserEmail) {\n        return (\n          <div className=\"space-y-1\">\n            {parsed.targetUserEmail && <div><span className=\"text-slate-400\">User:</span> <span className=\"text-white\">{String(parsed.targetUserEmail)}</span></div>}\n            {parsed.targetUserId && <div><span className=\"text-slate-400\">User ID:</span> <span className=\"text-slate-300\">{String(parsed.targetUserId)}</span></div>}\n            {parsed.amount && <div><span className=\"text-slate-400\">Amount:</span> <span className=\"text-green-400\">${String(parsed.amount)}</span></div>}\n            {parsed.reason && <div><span className=\"text-slate-400\">Reason:</span> <span className=\"text-white\">{String(parsed.reason)}</span></div>}\n          </div>\n        );\n      }\n\n      // Generic object formatting\n      return (\n        <div className=\"space-y-1\">\n          {Object.entries(parsed).map(([key, value]) => {\n            // Skip targetUser as we don't want to display it\n            if (key === 'targetUser') {\n              return null;\n            }\n\n            // Handle nested objects and arrays properly\n            let displayValue: string;\n            if (value === null || value === undefined) {\n              displayValue = 'N/A';\n            } else if (typeof value === 'object') {\n              // For other objects, format them nicely\n              try {\n                displayValue = JSON.stringify(value, null, 2);\n              } catch {\n                displayValue = '[Complex Object]';\n              }\n            } else {\n              displayValue = String(value);\n            }\n\n            return (\n              <div key={key}>\n                <span className=\"text-slate-400 capitalize\">{key.replace(/([A-Z])/g, ' $1').toLowerCase()}:</span>{' '}\n                <span className=\"text-white whitespace-pre-wrap\">{displayValue}</span>\n              </div>\n            );\n          }).filter(Boolean)}\n        </div>\n      );\n    }\n\n    return String(parsed);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-8 bg-slate-700 rounded w-1/4 mb-4\"></div>\n          <div className=\"h-64 bg-slate-700 rounded\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-white\">System Logs</h1>\n          <p className=\"text-slate-400 mt-1\">Monitor platform activity and user actions</p>\n        </div>\n        <Button\n          onClick={exportLogs}\n          variant=\"outline\"\n          className=\"flex items-center gap-2 border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white\"\n        >\n          <Download className=\"h-4 w-4\" />\n          Export Logs\n        </Button>\n      </div>\n\n      {/* Filters */}\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardContent className=\"p-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n            <div>\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4\" />\n                <Input\n                  placeholder=\"Search logs...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500\"\n                />\n              </div>\n            </div>\n            <div>\n              <select\n                value={filterAction}\n                onChange={(e) => setFilterAction(e.target.value)}\n                className=\"w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                <option value=\"all\">All Actions</option>\n                <option value=\"USER_LOGIN\">User Login</option>\n                <option value=\"USER_REGISTER\">User Register</option>\n                <option value=\"MINING_PURCHASE\">Mining Purchase</option>\n                <option value=\"WITHDRAWAL_REQUEST\">Withdrawal Request</option>\n                <option value=\"KYC_SUBMIT\">KYC Submit</option>\n                <option value=\"ADMIN_ACTION\">Admin Action</option>\n              </select>\n            </div>\n            <div>\n              <select\n                value={dateRange}\n                onChange={(e) => setDateRange(e.target.value as any)}\n                className=\"w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                <option value=\"today\">Today</option>\n                <option value=\"week\">This Week</option>\n                <option value=\"month\">This Month</option>\n                <option value=\"all\">All Time</option>\n              </select>\n            </div>\n            <div className=\"text-sm text-slate-400 flex items-center\">\n              <Activity className=\"h-4 w-4 mr-1\" />\n              {logs.length} logs found\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Logs Table */}\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2 text-white\">\n            <FileText className=\"h-5 w-5 text-blue-400\" />\n            Activity Logs\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-2\">\n            {logs.length === 0 ? (\n              <div className=\"text-center py-12\">\n                <FileText className=\"h-12 w-12 text-slate-400 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-medium text-white mb-2\">No Logs Found</h3>\n                <p className=\"text-slate-400\">No activity logs match your current filters.</p>\n              </div>\n            ) : (\n              logs.map((log) => (\n                <div\n                  key={log.id}\n                  className=\"bg-slate-700 rounded-lg border border-slate-600 hover:border-slate-500 transition-all duration-200 hover:shadow-lg\"\n                >\n                  {/* Header Section */}\n                  <div className=\"flex items-center justify-between p-4 border-b border-slate-600\">\n                    <div className=\"flex items-center gap-3\">\n                      <div className=\"p-2 rounded-full bg-slate-600\">\n                        {getActionIcon(log.action)}\n                      </div>\n                      <div>\n                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getActionColor(log.action)}`}>\n                          {log.action.replace(/_/g, ' ')}\n                        </span>\n                      </div>\n                    </div>\n                    <div className=\"text-sm text-slate-400\">\n                      {formatDate(log.createdAt)}\n                    </div>\n                  </div>\n\n                  {/* Content Section */}\n                  <div className=\"p-4 space-y-3\">\n                    {log.user && (\n                      <div className=\"flex items-center gap-2 p-2 bg-slate-800 rounded-lg\">\n                        <User className=\"h-4 w-4 text-blue-400\" />\n                        <div>\n                          <span className=\"font-medium text-white\">\n                            {log.user.firstName} {log.user.lastName}\n                          </span>\n                          <span className=\"text-slate-400 ml-2 text-sm\">({log.user.email})</span>\n                        </div>\n                      </div>\n                    )}\n\n                    {log.details && (\n                      <div className=\"p-3 bg-slate-800 rounded-lg\">\n                        <div className=\"text-sm font-medium text-slate-300 mb-2\">Details:</div>\n                        <div className=\"text-sm\">\n                          {formatLogDetails(log.details)}\n                        </div>\n                      </div>\n                    )}\n\n                    {(log.ipAddress || log.userAgent) && (\n                      <div className=\"p-2 bg-slate-800 rounded-lg\">\n                        <div className=\"text-xs text-slate-400 space-y-1\">\n                          {log.ipAddress && (\n                            <div className=\"flex items-center gap-2\">\n                              <span className=\"font-medium\">IP Address:</span>\n                              <span className=\"text-slate-300\">{log.ipAddress}</span>\n                            </div>\n                          )}\n                          {log.userAgent && (\n                            <div className=\"flex items-start gap-2\">\n                              <span className=\"font-medium whitespace-nowrap\">User Agent:</span>\n                              <span className=\"text-slate-300 break-all\">{log.userAgent}</span>\n                            </div>\n                          )}\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              ))\n            )}\n          </div>\n\n          {/* Pagination */}\n          {totalPages > 1 && (\n            <div className=\"flex items-center justify-between mt-6 pt-6 border-t border-slate-600\">\n              <div className=\"text-sm text-slate-400\">\n                Page {currentPage} of {totalPages}\n              </div>\n              <div className=\"flex gap-2\">\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}\n                  disabled={currentPage === 1}\n                  className=\"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white disabled:opacity-50\"\n                >\n                  Previous\n                </Button>\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}\n                  disabled={currentPage === totalPages}\n                  className=\"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white disabled:opacity-50\"\n                >\n                  Next\n                </Button>\n              </div>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AAjBA;;;;;;AAkCO,MAAM,aAAuB;IAClC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAChD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsC;IAC/E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAa;QAAY;QAAc;KAAU;IAErD,MAAM,YAAY;QAChB,IAAI;YACF,WAAW;YACX,MAAM,SAAS,IAAI,gBAAgB;gBACjC,MAAM,YAAY,QAAQ;gBAC1B,OAAO;gBACP,QAAQ;gBACR,QAAQ;gBACR;YACF;YAEA,MAAM,WAAW,MAAM,MAAM,CAAC,gBAAgB,EAAE,QAAQ,EAAE;gBACxD,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,QAAQ,KAAK,IAAI;oBACjB,cAAc,KAAK,UAAU;gBAC/B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,SAAS,IAAI,gBAAgB;gBACjC,QAAQ;gBACR,QAAQ;gBACR;gBACA,QAAQ;YACV;YAEA,MAAM,WAAW,MAAM,MAAM,CAAC,uBAAuB,EAAE,QAAQ,EAAE;gBAC/D,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;gBACvC,MAAM,IAAI,SAAS,aAAa,CAAC;gBACjC,EAAE,IAAI,GAAG;gBACT,EAAE,QAAQ,GAAG,CAAC,YAAY,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;gBACxE,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,EAAE,KAAK;gBACP,OAAO,GAAG,CAAC,eAAe,CAAC;gBAC3B,SAAS,IAAI,CAAC,WAAW,CAAC;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,0MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;YACL,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;gBACH,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC;gBACE,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,SAAS,OAAO;QAErB,IAAI;YACF,uCAAuC;YACvC,IAAI,OAAO,YAAY,UAAU,OAAO;YAExC,4CAA4C;YAC5C,IAAI,OAAO,YAAY,UAAU;gBAC/B,OAAO,KAAK,KAAK,CAAC;YACpB;YAEA,OAAO;QACT,EAAE,OAAM;YACN,+CAA+C;YAC/C,OAAO;QACT;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAS,gBAAgB;QAE/B,IAAI,CAAC,QAAQ,OAAO;QAEpB,IAAI,OAAO,WAAW,UAAU,OAAO;QAEvC,IAAI,OAAO,WAAW,UAAU;YAC9B,mDAAmD;YACnD,IAAI,OAAO,IAAI,KAAK,YAAY,OAAO,IAAI,KAAK,SAAS;gBACvD,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CAAI,8OAAC;oCAAK,WAAU;8CAAiB;;;;;;gCAAY;8CAAC,8OAAC;oCAAK,WAAU;8CAAc,OAAO,OAAO,IAAI;;;;;;;;;;;;sCACnG,8OAAC;;8CAAI,8OAAC;oCAAK,WAAU;8CAAiB;;;;;;gCAAc;8CAAC,8OAAC;oCAAK,WAAU;;wCAAiB;wCAAE,OAAO,OAAO,MAAM;;;;;;;;;;;;;sCAC5G,8OAAC;;8CAAI,8OAAC;oCAAK,WAAU;8CAAiB;;;;;;gCAAc;8CAAC,8OAAC;oCAAK,WAAU;8CAAc,OAAO,OAAO,MAAM;;;;;;;;;;;;wBACtG,OAAO,WAAW,kBAAI,8OAAC;;8CAAI,8OAAC;oCAAK,WAAU;8CAAiB;;;;;;gCAAmB;8CAAC,8OAAC;oCAAK,WAAU;8CAAc,OAAO,OAAO,WAAW;;;;;;;;;;;;;;;;;;YAG9I;YAEA,wCAAwC;YACxC,IAAI,OAAO,UAAU,IAAI,OAAO,OAAO,UAAU,KAAK,UAAU;gBAC9D,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CAAI,8OAAC;oCAAK,WAAU;8CAAiB;;;;;;gCAAY;8CAAC,8OAAC;oCAAK,WAAU;8CAAc,OAAO,OAAO,IAAI;;;;;;;;;;;;sCACnG,8OAAC;;8CAAI,8OAAC;oCAAK,WAAU;8CAAiB;;;;;;gCAAc;8CAAC,8OAAC;oCAAK,WAAU;;wCAAiB;wCAAE,OAAO,OAAO,MAAM;;;;;;;;;;;;;wBAC3G,OAAO,MAAM,kBAAI,8OAAC;;8CAAI,8OAAC;oCAAK,WAAU;8CAAiB;;;;;;gCAAc;8CAAC,8OAAC;oCAAK,WAAU;8CAAc,OAAO,OAAO,MAAM;;;;;;;;;;;;wBACxH,OAAO,WAAW,kBAAI,8OAAC;;8CAAI,8OAAC;oCAAK,WAAU;8CAAiB;;;;;;gCAAmB;8CAAC,8OAAC;oCAAK,WAAU;8CAAc,OAAO,OAAO,WAAW;;;;;;;;;;;;wBACvI,OAAO,eAAe,KAAK,2BAAa,8OAAC;;8CAAI,8OAAC;oCAAK,WAAU;8CAAiB;;;;;;gCAAwB;8CAAC,8OAAC;oCAAK,WAAU;;wCAAiB;wCAAE,OAAO,OAAO,eAAe;;;;;;;;;;;;;wBACvK,OAAO,UAAU,KAAK,2BAAa,8OAAC;;8CAAI,8OAAC;oCAAK,WAAU;8CAAiB;;;;;;gCAAmB;8CAAC,8OAAC;oCAAK,WAAU;;wCAAiB;wCAAE,OAAO,OAAO,UAAU;;;;;;;;;;;;;;;;;;;YAG/J;YAEA,IAAI,OAAO,YAAY,IAAI,OAAO,eAAe,EAAE;gBACjD,qBACE,8OAAC;oBAAI,WAAU;;wBACZ,OAAO,eAAe,kBAAI,8OAAC;;8CAAI,8OAAC;oCAAK,WAAU;8CAAiB;;;;;;gCAAY;8CAAC,8OAAC;oCAAK,WAAU;8CAAc,OAAO,OAAO,eAAe;;;;;;;;;;;;wBACxI,OAAO,YAAY,kBAAI,8OAAC;;8CAAI,8OAAC;oCAAK,WAAU;8CAAiB;;;;;;gCAAe;8CAAC,8OAAC;oCAAK,WAAU;8CAAkB,OAAO,OAAO,YAAY;;;;;;;;;;;;wBACzI,OAAO,MAAM,kBAAI,8OAAC;;8CAAI,8OAAC;oCAAK,WAAU;8CAAiB;;;;;;gCAAc;8CAAC,8OAAC;oCAAK,WAAU;;wCAAiB;wCAAE,OAAO,OAAO,MAAM;;;;;;;;;;;;;wBAC7H,OAAO,MAAM,kBAAI,8OAAC;;8CAAI,8OAAC;oCAAK,WAAU;8CAAiB;;;;;;gCAAc;8CAAC,8OAAC;oCAAK,WAAU;8CAAc,OAAO,OAAO,MAAM;;;;;;;;;;;;;;;;;;YAG/H;YAEA,4BAA4B;YAC5B,qBACE,8OAAC;gBAAI,WAAU;0BACZ,OAAO,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM;oBACvC,iDAAiD;oBACjD,IAAI,QAAQ,cAAc;wBACxB,OAAO;oBACT;oBAEA,4CAA4C;oBAC5C,IAAI;oBACJ,IAAI,UAAU,QAAQ,UAAU,WAAW;wBACzC,eAAe;oBACjB,OAAO,IAAI,OAAO,UAAU,UAAU;wBACpC,wCAAwC;wBACxC,IAAI;4BACF,eAAe,KAAK,SAAS,CAAC,OAAO,MAAM;wBAC7C,EAAE,OAAM;4BACN,eAAe;wBACjB;oBACF,OAAO;wBACL,eAAe,OAAO;oBACxB;oBAEA,qBACE,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCAA6B,IAAI,OAAO,CAAC,YAAY,OAAO,WAAW;oCAAG;;;;;;;4BAAS;0CACnG,8OAAC;gCAAK,WAAU;0CAAkC;;;;;;;uBAF1C;;;;;gBAKd,GAAG,MAAM,CAAC;;;;;;QAGhB;QAEA,OAAO,OAAO;IAChB;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAIvB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAgC;;;;;;0CAC9C,8OAAC;gCAAE,WAAU;0CAAsB;;;;;;;;;;;;kCAErC,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,SAAQ;wBACR,WAAU;;0CAEV,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;;0BAMpC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CACC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC,iIAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;;;;;;;;;;;;;;;;0CAIhB,8OAAC;0CACC,cAAA,8OAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oCAC/C,WAAU;;sDAEV,8OAAC;4CAAO,OAAM;sDAAM;;;;;;sDACpB,8OAAC;4CAAO,OAAM;sDAAa;;;;;;sDAC3B,8OAAC;4CAAO,OAAM;sDAAgB;;;;;;sDAC9B,8OAAC;4CAAO,OAAM;sDAAkB;;;;;;sDAChC,8OAAC;4CAAO,OAAM;sDAAqB;;;;;;sDACnC,8OAAC;4CAAO,OAAM;sDAAa;;;;;;sDAC3B,8OAAC;4CAAO,OAAM;sDAAe;;;;;;;;;;;;;;;;;0CAGjC,8OAAC;0CACC,cAAA,8OAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;oCAC5C,WAAU;;sDAEV,8OAAC;4CAAO,OAAM;sDAAQ;;;;;;sDACtB,8OAAC;4CAAO,OAAM;sDAAO;;;;;;sDACrB,8OAAC;4CAAO,OAAM;sDAAQ;;;;;;sDACtB,8OAAC;4CAAO,OAAM;sDAAM;;;;;;;;;;;;;;;;;0CAGxB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCACnB,KAAK,MAAM;oCAAC;;;;;;;;;;;;;;;;;;;;;;;0BAOrB,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,8MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAA0B;;;;;;;;;;;;kCAIlD,8OAAC,gIAAA,CAAA,cAAW;;0CACV,8OAAC;gCAAI,WAAU;0CACZ,KAAK,MAAM,KAAK,kBACf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;4CAAG,WAAU;sDAAsC;;;;;;sDACpD,8OAAC;4CAAE,WAAU;sDAAiB;;;;;;;;;;;2CAGhC,KAAK,GAAG,CAAC,CAAC,oBACR,8OAAC;wCAEC,WAAU;;0DAGV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACZ,cAAc,IAAI,MAAM;;;;;;0EAE3B,8OAAC;0EACC,cAAA,8OAAC;oEAAK,WAAW,CAAC,oEAAoE,EAAE,eAAe,IAAI,MAAM,GAAG;8EACjH,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM;;;;;;;;;;;;;;;;;kEAIhC,8OAAC;wDAAI,WAAU;kEACZ,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,IAAI,SAAS;;;;;;;;;;;;0DAK7B,8OAAC;gDAAI,WAAU;;oDACZ,IAAI,IAAI,kBACP,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;;kFACC,8OAAC;wEAAK,WAAU;;4EACb,IAAI,IAAI,CAAC,SAAS;4EAAC;4EAAE,IAAI,IAAI,CAAC,QAAQ;;;;;;;kFAEzC,8OAAC;wEAAK,WAAU;;4EAA8B;4EAAE,IAAI,IAAI,CAAC,KAAK;4EAAC;;;;;;;;;;;;;;;;;;;oDAKpE,IAAI,OAAO,kBACV,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAA0C;;;;;;0EACzD,8OAAC;gEAAI,WAAU;0EACZ,iBAAiB,IAAI,OAAO;;;;;;;;;;;;oDAKlC,CAAC,IAAI,SAAS,IAAI,IAAI,SAAS,mBAC9B,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;gEACZ,IAAI,SAAS,kBACZ,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAc;;;;;;sFAC9B,8OAAC;4EAAK,WAAU;sFAAkB,IAAI,SAAS;;;;;;;;;;;;gEAGlD,IAAI,SAAS,kBACZ,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAgC;;;;;;sFAChD,8OAAC;4EAAK,WAAU;sFAA4B,IAAI,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCAvDhE,IAAI,EAAE;;;;;;;;;;4BAoElB,aAAa,mBACZ,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;4CAAyB;4CAChC;4CAAY;4CAAK;;;;;;;kDAEzB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;gDACzD,UAAU,gBAAgB;gDAC1B,WAAU;0DACX;;;;;;0DAGD,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,YAAY,OAAO;gDAClE,UAAU,gBAAgB;gDAC1B,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}]}